#!/usr/bin/env bash

# 环境，默认为dev
NAMESPACE=${1:-"dev"}
if [ -f ./k8s_config_${NAMESPACE}.sh ]; then
    source ./k8s_config_${NAMESPACE}.sh
else
    source ./k8s_config.sh
fi
set -eo pipefail


DEPLOYMENT_NAME=${APP_NAME}
IMAGE_NAME=${APP_NAME}
IMAGE_URL="${K8S_URL}/${K8S_REGISTRY_NAME}/${IMAGE_NAME}"
YAML_FILE="k8s.yaml"
IMAGE_TAG=`docker images |grep ${IMAGE_URL} |awk 'NR==1 {print $2}'`

export IMAGE_TAG=${IMAGE_TAG}
export IMAGE_NAME=${IMAGE_NAME}
export IMAGE_URL=${IMAGE_URL}
export DEPLOYMENT_NAME=${DEPLOYMENT_NAME}
export NAMESPACE=${NAMESPACE}

# shellcheck disable=SC2034
for unit in "${UNIT_NO_LIST[@]}"; do
    export K8S_NAMESPACE=${APP_ID}-${NAMESPACE}-${unit}
    export ENV_UNIT_NO=${unit}
    echo ${DOCKER_NODE_PORT}
    # 使用 envsubst 替换模板中的变量，生成 YAML 文件
    if ! envsubst < ${YAML_FILE}.template > ${YAML_FILE}; then
        echo "Error: Failed to substitute environment variables in the template."
        exit 1
    fi

    echo "starting deployment"
    # 应用生成的 YAML 文件到 Kubernetes
    if ! kubectl apply -f ${YAML_FILE} -n ${K8S_NAMESPACE} --record; then
        echo "Error: Failed to apply the YAML file to Kubernetes."
        exit 1
    fi
    kubectl rollout restart deployment/anytxn-tech-front -n ${K8S_NAMESPACE}

    echo "kubectl rollout restart successfully"
done





