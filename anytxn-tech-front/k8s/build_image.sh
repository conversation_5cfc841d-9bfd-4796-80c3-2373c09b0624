#!/usr/bin/env bash
# 环境，默认为dev
NAMESPACE=${1:-"dev"}
if [ -f ./k8s_config_${NAMESPACE}.sh ]; then
    source ./k8s_config_${NAMESPACE}.sh
else
    source ./k8s_config.sh
fi
set -eo pipefail

TIME=`date "+%Y%m%d%H%M"`
REGISTRY_IP=${K8S_URL}
REGISTRY_NAME=${K8S_REGISTRY_NAME}

modules=${DOCKER_MODULES}

if [ -f ./Dockerfile_${NAMESPACE} ]; then
    DOCKERFILE="./Dockerfile_${NAMESPACE}"
else
    DOCKERFILE="./Dockerfile"
fi

echo "namespace is ${NAMESPACE}, Dockerfile is ${DOCKERFILE}"

for module in ${modules}
do
    echo "Starting to build Docker image for module: ${module}"
    echo "****Docker image tag will be: ${REGISTRY_IP}/${REGISTRY_NAME}/${module}:${TIME} **"
    docker build --no-cache -t "${REGISTRY_IP}/${REGISTRY_NAME}/${module}:${TIME}" --file ${DOCKERFILE} .
    if [ $? -eq 0 ]
    then
        echo "Docker image for module ${module} built successfully."
        docker push "${REGISTRY_IP}/${REGISTRY_NAME}/${module}:${TIME}"
        if [ $? -eq 0 ]
        then
            echo "Docker image for module ${module} pushed successfully to registry."
        else
            echo "Failed to push Docker image for module ${module}."
        fi
    else
        echo "Failed to build Docker image for module ${module}."
    fi
done