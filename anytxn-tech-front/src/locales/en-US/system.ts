// 系统管理模块

// 角色管理页面（英文）
const role = {
  roleId: 'Role Id', // 角色Id
  roleStatus: 'Role Status', // 角色状态
  mountStatus: 'Mount Status', // 挂载状态
  action: 'Action', // 操作
  roleName: 'Role Name', // 角色名称
  enable: 'Enable', // 启用
  unEnable: 'UnEnable', // 停用
  mounted: 'Mounted', // 已挂载
  unmounted: 'UnMounted', // 未挂载
  roleNamePlaceholder: 'Please enter the role name', // 角色名悬浮提示
  roleStatusPlaceholder: 'Please select the role status', // 角色状态悬浮提示
  mountStatusPlaceholder: 'Please select the mounting status', // 挂在状态悬浮提示
  viewMount: 'View mounting details', // 查看挂载详情
  editMount: 'Edit Mount', // 编辑挂载
  tip: 'tip', // 提示
  editMountSuccess: 'Edit role permissions mounted successfully', // 编辑角色权限挂载成功
  editSuccess: 'Edit role successfully!', // 编辑成功!
  addSuccess: 'Add role successfully!', // 新增成功!
  view: 'view', // 查询
  reset: 'reset', // 重置
  add: 'add', // 新增
  roleDesc: 'roleDesc', // 角色描述
  roleIdPlaceholder: 'Please enter the role ID', // 请输入角色Id
  roleDescPlaceholder: 'Please enter role description', // 请输入角色描述
  limit16: 'Cannot exceed 16 words', // 不能超过16个字
  limit2: 'Cannot exceed 2 words', // 不能超过2个字
  limit200: 'Cannot exceed 200 words', // 不能超过200个字
  role: 'Role', // 角色
  copyObject: 'Copy object', // 复制对象
  menuPermissions: 'Menu permissions', // 选单权限
  viewMountDetail: 'View role menu mounting', // 查看角色选单挂载
  editMountDetail: 'Edit role menu mounting', // 编辑角色选单挂载
  mountDetail: 'Role menu mounting', // 角色挂载
  warningMsg: 'Deactivate role cannot be edited', // 停用角色不可编辑
};

// 选单管理页面（英文）
const menu = {
  menuName: 'Menu Name', // 选单名称
  menuStatus: 'Menu status', // 选单状态
  open: 'open', // 开启
  close: 'close', // 关闭
  menuNamePlaceholder: 'Please enter the menu name', // 请输入选单名称
  menuStatusPlaceholder: 'Please select the menu status', // 请选择状态
  sort: 'sort', // 排序
  menuType: 'Menu type', // 选单类型
  createTs: 'Creation time', // 创建时间
  updateTs: 'Update time', // 更新时间
  menu: 'menu', // 选单
  button: 'button', // 按钮
  menuInfo: 'Menu information', // 选单信息
  expanded: 'expanded', // 展开
  unexpanded: 'collapse', // 折叠
  opFailed: 'operation failed', // 操作失败
  supMenu: 'Superior menu', // 上级选单
  supMenuPlaceholder: 'Please select the superior menu', // 请选择上级选单
  menuTypePlaceholder: 'Please select the menu type', // 请选择选单类型
  limit150: 'Cannot exceed 150 words', // 不能超过150个字
  routeUrl: 'Routing address', // 路由地址
  displaySorting: 'Display sorting', // 展示排序
  displaySortPlaceholder: 'Please enter display sorting', // 请输入展示排序
  save: 'save',
  permissionId: ' Permission identifier ',
  iconId: ' Icon ',
  iconSelect: ' Icon selection ',
  direction: ' Directional icon ',
  suggestion: ' Suggested icon prompts',
  editor: ' Edit class icon ',
  data: ' Data type icon ',
  logo: ' Brand and logo',
  other: ' Website universal icon ',
  messTip: ' The currently selected icon is: ',
  addIcon: 'Selected Icon',
};
const communication = {
  passageway: 'channel',
  gongneng: 'Function Type',
  VSISS: 'VISA CARD ISSUE SIDE',
  VSACQ: 'VISA receiving side',
  signIn: 'Check-in',
  signOut: 'sign out',
  startTransmission: 'Start transmission',
  stopTransmission: 'stop transmission',
  MCISS: ' MasterCard Card Side',
  MCACQ: ' MasterCard Receipt Side',
  someSignIn: 'Partial check-in',
  someSignOut: 'Partially signed out',
  NCISS: 'NCCC card issuer',
  NCACQ: 'NCCC receiving side',
  keyTransmission: 'key exchange',
  CPACQS: 'CARDPOOL CARD SIDE',
  CPACQF: 'CARDPOOL SINGLE SIDE',
  visa: 'VISA',
  masterCard: 'MasterCard',
  nccc: 'NCCC',
  cardpool: 'CARDPOOL',
  chanelStatus: 'Chanel Status',
  chanel: 'Channel',
  communicationStatus: 'Communication Status',
  SFM: 'SFM Network Management', // SFM网络管理
  lastOperateTime: 'Last Operation Time', // 最后操作时间
  chanelComStatus: 'Channel Communication Status Query', // 管道通讯状态查询
};

const dictionary = {
  dictionary: 'Dictionary Information', // 字典资讯
  dictId: 'Dictionary ID', // 字典id
  dictCode: 'Dictionary Code', // 字典编码
  dictName: 'Dictionary Name', // 字典名称
  dictDesc: 'Dictionary Description', // 字典描述
  status: 'Status', // 状态
  dictKey: 'Dictionary Key', // 字典值编码
  dictValue: 'Dictionary Value', // 字典值名称
  parentDictKey: 'Parent Dictionary Key', // 父级字典值
  dictionaryValue: 'Dictionary Value', // 字典值
  cardBin: 'CARD BIN',
};

// 登录日志
const loginLog = {
  loginLog: 'Login Log', // 登录日志
  userId: 'User ID', // 用户ID
  userName: 'User Name', // 用户名称
  loginTime: 'Login Time', // 登录时间
  loginType: 'Login Type', // 登录类型
  loginStatus: 'Login Status', // 登录状态
  clientIp: 'Client IP', // 登录IP
  serverName: 'Server Name', // 登录伺服器名称
  login: 'Login', // 登入
  logout: 'Logout', // 登出
  fail: 'Fail', // 失败
  success: 'Success', // 成功
};

// 操作日志
const operateLog = {
  operateLog: 'Operation Log', // 操作日志
  menuName: 'Menu Name', // 选单名称
  operateTime: 'Operation Time', // 操作时间
  operateServName: 'Service Name', // 介面名称
  operateType: 'Operation Type', // 操作类型
  operateStatus: 'Operation Status', // 操作状态
  operateDetail: 'Operation Detail', // 操作详情
  create: 'Create', // 新增
  delete: 'Delete', // 删除
  update: 'Update', // 修改
  query: 'Query', // 查询
  transfer: 'Transfer', // AP to AP传输
  reportForm: 'Report Form', // 报表
  exportDownload: 'Export Download', // 汇出下载
  print: 'Print', // 列印
};

export default { ...role, ...menu, ...communication, ...dictionary, ...loginLog, ...operateLog };
