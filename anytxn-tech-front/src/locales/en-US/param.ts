export default {
  // 接口配置参数
  baseInfo: 'BaseInfo',
  paramIndex: 'sequence number',
  interfaceConfig: 'Interface configuration',
  msgId: 'Trading code',
  Sts: 'state',
  clusterGroup: 'set group',
  Module: 'module',
  createTime: 'Create time',
  updateTime: 'Update time',
  invalid: 'Disable',
  effective: 'Enable',
  oldModel: 'Old',
  newModel: 'New',
  switchOpne: 'Open',
  switchClose: 'Off',
  delete: 'Delete',
  interfaceType: 'Interface Type',
  internal: 'Internal',
  channel: 'Channel',
  // 接口映射参数
  interfaceMapping: 'Interface Mapping',
  createUser: 'record creator',
  updateUser: 'record modifier',
  sourceChannel: 'Source Channel',
  url: 'Specify the recipient',
  protocol: 'Protocol',
  linkMode: 'Link Mode',
  dualSwitch: 'double-engine switch',
  recordSwitch: 'record switch',
  func: 'Func',
  host: 'address',
  // 双方白名单服务
  doubleWhite: 'Whitelist service on both sides',
  custType: 'Customer identification type',
  custRecogNo: 'Customer Identification Number',
  status: 'Status',
  // 错误代码映射参数
  errorCode: 'Error code mapping parameter',
  newErrCode: 'New Error Code',
  errCode: 'Error Code',
  errMsg: 'Error message',
  converConfig: 'Conversion Configuration Find',
  converConfigUrl: 'New interface address',
  configType: 'Configure message type',
  inConfigContext: 'Request Configuration Information',
  outConfigContext: 'Response configuration information',
  // 查找配置参数
  searchConfig: 'Find configuration parameters',
  searchConfigKey: 'Key',
  searchConfigValue: 'Value',
  configDesc: 'Configuration description',
  // api密钥映射应用服务
  apiKeyMapping: 'Api Key Mapping',
  apiKey: 'api key',
  apiMsgId: 'Message id',
  // api密钥配置应用服务
  apiKeyConfig: 'Api Key Config',
  apiSourceChannel: 'Source Channel',
  activationTime: 'Activation Time',
  expirationTime: 'Expiration time',
  // 交易网关/出口网关（网络管理）
  cardIssuingNetManage: 'Network Management',
  cardGroup: 'channel',
  VISA: 'VISA CARD ISSUE SIDE',
  VSACQ: 'VISA receiving side',
  signIn: 'Check-in',
  signOut: 'sign out',
  startTransmission: 'Start transmission',
  stopTransmission: 'stop transmission',
  MC: ' MasterCard Card Side',
  MCACQ: ' MasterCard Receipt Side',
  someSignIn: 'Partial check-in',
  someSignOut: 'Partially signed out',
  NCCC: 'NCCC card issuer',
  NCACQ: 'NCCC receiving side',
  keyTransmission: 'key exchange',
  CARD: 'CARDPOOL CARD SIDE',
  CPACQF: 'CARDPOOL SINGLE SIDE',
  visa: 'VISA',
  masterCard: 'MasterCard',
  nccc: 'NCCC',
  cardpool: 'CARDPOOL',
  chanelStatus: 'Chanel Status',
  chanel: 'Channel',
  chanlState: 'Communication Status',
  cardBin: 'CARD BIN',
  SFM: 'SFM Network Management', // SFM网络管理
  lastOperateTime: 'Last Operation Time', // 最后操作时间
  chanelComStatus: 'Channel Communication Status Query', // 管道通讯状态查询
};
