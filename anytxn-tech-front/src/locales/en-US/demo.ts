export default {
  authorizationResCode: 'Authorization response code parameters',
  baseInfo: 'Basic information',
  paramIndex: 'No.',
  rejRsnCode: 'Reject Reason Code',
  crcdOrgNo: 'Credit card institution number',
  rejRsnCodePri: 'Reject Reason Code Priority',
  rejRsnCodeDesc: 'Reject reason code description',
  sysInnerRespCode: 'System Internal Response Code',
  mcsRespCode: 'MCS Response Code',
  mcRespCode: 'MasterCo Response Code',
  visaRespCode: 'VISA Response Code',
  ecsRespCode: 'ECS Response Code',
  createTlrNo: 'Create a teller',
  updTlrNo: 'Update the teller',
  createTlrOrgNo: 'Create a teller',
  updTlrOrgNo: 'Update the teller agency number',
  updateUser: 'Update User',
  version: 'Version Number',
  creaateTime: 'Create a timestamp',
  updateTime: 'Update timestamp',
  Param_sts: 'parameter state',
  invalid: 'Invalid',
  effective: 'Effective',
  delete: 'Delete',
};