// 通用变量（英文）
export default {
  create: 'Create', // 新增
  detail: 'Detail', // 查看
  copy: 'Copy', // 复制
  edit: 'Edit', // 编辑
  delete: 'Delete', // 删除
  option: 'Operation', // 操作
  search: 'Search', // 查询
  reset: 'Reset', // 重置
  submit: 'Confirm', // 提交
  save: 'Save', // 保存
  back: 'Back', // 返回
  maintain: 'Maintain', // 维护
  up: 'Up', // 上移
  down: 'Down', // 下移
  inputPlaceholder: 'Please enter', // 请输入
  selectPlaceholder: 'Please select', // 请选择
  invalid: 'Invalid', // 生效
  effective: 'Effective', // 失效
  baseInfo: 'Basic information',
  confirm: 'Confirm', // 确定
  cancel: 'Cancel', // 取消
  confirmDelete: 'Are you sure you want to delete?', // 确定删除吗？
  editSuccess: 'Edited successfully', // 编辑成功
  checkMsg: 'Please check the format', // 请检查格式
  paramSts: 'Param Status', // 状态
  crcdOrgNo: 'Credit Card Organization No.', // 信用卡机构号
  version: 'Version', // 版本号
  updateTime: 'Update Time', // 更新时间
  success: 'success', // 成功
  fail: 'failed', // 失败
  bankName: 'CUB',
  tip: 'Tip', // 提示
  createSuccess: 'Create successful',
  createFailed: 'New failed',
  editFailed: 'Editing failed', // 编辑失败
  deleteFailed: 'Delete failed', // 删除失败
  deleteSuccess: 'Deletion successful', // 删除成功
  detailFailed: 'Failed to retrieve details', // 获取详情失败
  copySuccess: 'Copy successful',
  copyFailed: 'Copy failed',
  expanded: 'expanded', // 展开
  unexpanded: 'collapse', // 折叠
  operationSuccess: 'Operation successful', // 操作成功
  greenMain: 'Aurora green',
  blueMain: 'Dawn Blue',
  redMain: 'Volcanic Red',
  operationManual: 'Operation Manual',
  guide: 'Guide',
  updateLog: 'Update Log',
  createTime: 'Create Time', // 创建时间
  createUser: 'Creator', // 创建人
  updateUser: 'Last Updated By', // 最近更新人
  enabled: 'Enabled', // 启用
  disabled: 'Disabled', // 未启用
  paramIndex: 'Serial Number', // 序号
  orgnNmbR: 'Account Currency',
  trxCcy: 'Trading Currency',
  billingCcy: 'Account currencies',
  TWD: 'TWD',
  USD: 'USD',
  RMB: 'RMB',
  crcdCcy: 'Credit Card Currency', // 信用卡卡片币种
  formEditing: 'Editing in progress, please save and try again', // 正在编辑中，请保存后重试
  firstRow: 'This is the first piece of data', // 已是第一條數據
  lastRow: 'This is the last piece of data', // 已是最后一条数据
  previousPage: 'Previous',
  nextPage: 'Next',
  page_10: '10/ page',
  page_20: '20/ page',
  page_50: '50/ page',
  page_100: '100/ page',
  tip_404: 'The page you are visiting does not exist',
  nodata: 'No Data',
  userName: 'Username', // 用户名
  empName: 'Department Name', // 部门名
  branchName: 'Branch Name', // 分行名称
  logout: 'Logout', // 登出
  menuSearchPlaceHolder: 'Please enter the menu name', // 请输入选单名称
};
