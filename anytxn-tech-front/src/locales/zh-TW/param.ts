export default {
  // 接口配置參數
  incId: 'id',
  baseInfo: '基礎信息',
  paramIndex: '序號',
  interfaceConfig: '接口配置參數',
  msgId: '交易碼',
  status: '狀態',
  clusterGroup: '集群組',
  module: '所屬模塊',
  createTime: '創建時間',
  updateTime: '更新時間',
  invalid: '禁用',
  effective: '啟用',
  oldModel: '舊',
  newModel: '新',
  switchOpne: '開',
  switchClose: '關',
  delete: '刪除',
  interfaceType: '接口類型',
  internal: '內部',
  channel: '渠道',
  // 接口映射參數
  interfaceMapping: '接口映射參數',
  createUser: '記錄創建人',
  updateUser: '記錄修改人',
  sourceChannel: '消息來源渠道',
  url: '指定接收方',
  protocol: '協議',
  linkMode: '鏈路模式',
  dualSwitch: '雙發開關',
  recordSwitch: '錄製開關',
  func: '功能',
  host: '地址',
  // 雙方白名單服務
  doubleWhite: '雙方白名單服務',
  custType: '客戶鑒別類型',
  custRecogNo: '客戶鑒別編號',
  // 錯誤代碼映射參數
  errorCode: '錯誤代碼映射參數',
  newErrCode: '新錯誤代碼',
  errCode: '錯誤代碼',
  errMsg: '錯誤消息',
  converConfig: '轉換配置查找',
  converConfigUrl: '新接口地址',
  configType: '配置報文類型',
  inConfigContext: '請求配置信息',
  outConfigContext: '響應配置信息',
  // 查找配置參數
  searchConfig: '查找配置參數',
  searchConfigKey: '鑰匙',
  searchConfigValue: '值',
  configDesc: '配置描述',
  // api密鑰映射應用服務
  apiKeyMapping: 'api密鑰映射應用服務',
  apiKey: 'api密鑰',
  apiMsgId: '消息id',
  // api密鑰配置應用服務
  apiKeyConfig: 'api密鑰配置應用服務',
  apiSourceChannel: '源信道',
  activationTime: '激活時間',
  expirationTime: '到期時間',
  // 交易网关/出口网关（网络管理）
  cardIssuingNetManage: '网络管理',
  cardGroup: '對應卡組織前置',
  VISA: 'VISA發卡側',
  VSACQ: 'VISA收單側',
  signIn: '簽到',
  signOut: '簽退',
  startTransmission: '開始傳輸',
  stopTransmission: '停止傳輸',
  MC: '萬事達發卡側',
  MCACQ: '萬事達收單側',
  someSignIn: '部分簽到',
  someSignOut: '部分簽退',
  NCCC: 'NCCC發卡側',
  NCACQ: 'NCCC收單側',
  keyTransmission: '密鑰交換',
  CARD: 'CARDPOOL發卡側',
  CPACQF: 'CARDPOOL收單側',
  visa: 'VISA網絡管理',
  masterCard: '萬事達網絡管理',
  nccc: 'NCCC網絡管理',
  cardpool: 'CARDPOOL網絡管理',
  chanelStatus: '渠道簽到狀態查詢',
  chanel: '通道',
  chanlState: '通訊狀態',
  cardBin: '卡BIN',
  SFM: 'SFM網路管理',
  lastOperateTime: '最後操作時間',
  chanelComStatus: '管道通訊狀態查詢',
};
