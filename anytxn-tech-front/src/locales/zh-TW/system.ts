// 系统管理模块

// 角色管理页面（繁体中文）
const role = {
  roleId: '角色序號', // 角色Id
  roleStatus: '角色狀態', // 角色状态
  mountStatus: '掛載狀態', // 挂载状态
  action: '操作', // 操作
  roleName: '角色名稱', // 角色名称
  enable: '啟用', // 啟用
  unEnable: '停用', // 停用
  mounted: '已掛載', // 已掛載
  unmounted: '未掛載', // 未掛載
  roleNamePlaceholder: '請輸入角色名稱', // 角色名懸浮提示
  roleStatusPlaceholder: '請選擇角色狀態', // 角色狀態懸浮提示
  mountStatusPlaceholder: '請選擇掛載狀態', // 掛載狀態懸浮提示
  viewMount: '查看掛載詳情', // 查看掛載詳情
  editMount: '編輯掛載', // 編輯掛載
  tip: '提示', // 提示
  editMountSuccess: '編輯角色許可權掛載成功', // 編輯角色許可權掛載成功
  editSuccess: '編輯成功!', // 編輯成功!
  addSuccess: '新增成功!', // 新增成功!
  view: '查詢', // 查詢
  reset: '重置', // 重置
  add: '新增', // 新增
  roleDesc: '角色描述', // 角色描述
  roleIdPlaceholder: '請輸入角色Id', // 請輸入角色Id
  roleDescPlaceholder: '請輸入角色描述', // 請輸入角色描述
  limit16: '不能超過16個字', // 不能超過16個字
  limit2: '不能超過2個字', // 不能超過2個字
  limit200: '不能超過200個字', // 不能超過200個字
  role: '角色', // 角色
  copyObject: '複製對象', // 複製對象
  menuPermissions: '菜單許可權', // 菜單許可權
  viewMountDetail: '查看角色選單掛載', // 查看角色選單掛載
  editMountDetail: '編輯角色選單掛載', // 編輯角色選單掛載
  mountDetail: '角色掛載', // 角色挂载
  warningMsg: '停用角色不可編輯',
};

// 选单管理页面（繁体中文）
const menu = {
  menuName: '選單名稱', // 選單名稱
  menuStatus: '選單狀態', // 選單狀態
  open: '開啟', // 開啟
  close: '關閉', // 關閉
  menuNamePlaceholder: '請輸入選單名稱', // 請輸入選單名稱
  menuStatusPlaceholder: '請選擇狀態', // 請選擇狀態
  sort: '排序', // 排序
  menuType: '選單類型', // 選單類型
  createTs: '創建時間', // 創建時間
  updateTs: '更新時間', // 更新時間
  menu: '選單', // 選單
  button: '按鈕', // 按鈕
  menuInfo: '選單資訊', // 選單資訊
  expanded: '展開', // 展開
  unexpanded: '折疊', // 折疊
  opFailed: '操作失敗', // 操作失敗
  supMenu: '上級選單', // 上級選單
  supMenuPlaceholder: '請選擇上級選單', // 請選擇上級選單
  menuTypePlaceholder: '請選擇選單類型', // 請選擇選單類型
  limit150: '不能超過150個字', // 不能超過150個字
  routeUrl: '路由地址', // 路由地址
  displaySorting: '展示排序', // 展示排序
  displaySortPlaceholder: '請輸入展示排序', // 請輸入展示排序
  save: '儲存',
  permissionId: '權限標識',
  iconId: '圖標',
  iconSelect: '圖標選擇',
  direction: '方向性圖標',
  suggestion: '提示建議性圖標',
  editor: '編輯類圖標',
  data: '數據類圖標',
  logo: '品牌和標識',
  other: '網站通用圖標',
  messTip: '當前選擇圖標：',
  addIcon: '選擇圖標',
};

const communication = {
  passageway: '通道',
  gongneng: '功能類型',
  VSISS: 'VISA發卡側',
  VSACQ: 'VISA收單側',
  signIn: '簽到',
  signOut: '簽退',
  startTransmission: '開始傳輸',
  stopTransmission: '停止傳輸',
  MCISS: '萬事達發卡側',
  MCACQ: '萬事達收單側',
  someSignIn: '部分簽到',
  someSignOut: '部分簽退',
  NCISS: 'NCCC發卡側',
  NCACQ: 'NCCC收單側',
  keyTransmission: '密鑰交換',
  CPACQS: 'CARDPOOL發卡側',
  CPACQF: 'CARDPOOL收單側',
  visa: 'VISA網絡管理',
  masterCard: '萬事達網絡管理',
  nccc: 'NCCC網絡管理',
  cardpool: 'CARDPOOL網絡管理',
  chanelStatus: '渠道簽到狀態查詢',
  chanel: '通道',
  communicationStatus: '通訊狀態',
  SFM: 'SFM網路管理',
  lastOperateTime: '最後操作時間',
  chanelComStatus: '管道通訊狀態查詢',
};

// 字典配置页面
const dictionary = {
  dictionary: '字典資訊',
  dictId: '字典id',
  dictCode: '字典編碼',
  dictName: '字典名稱',
  dictDesc: '字典描述',
  status: '狀態',
  dictKey: '字典值編碼',
  dictValue: '字典值名稱',
  parentDictKey: '父級字典值',
  dictionaryValue: '字典值',
  cardBin: '卡BIN',
};

// 登录日志
const loginLog = {
  loginLog: '登錄日志',
  userId: '用戶ID',
  userName: '用戶名稱',
  loginTime: '登錄時間',
  loginType: '登錄類型',
  loginStatus: '登錄狀態',
  clientIp: '登錄IP',
  serverName: '登錄伺服器名稱',
  login: '登入',
  logout: '登出',
  fail: '失敗',
  success: '成功',
};

// 操作日志
const operateLog = {
  operateLog: '操作日志',
  menuName: '選單名稱',
  operateTime: '操作時間',
  operateServName: '介面名稱',
  operateType: '操作類型',
  operateStatus: '操作狀態',
  operateDetail: '操作詳情',
  create: '新增',
  delete: '刪除',
  update: '修改',
  query: '查詢',
  transfer: 'AP to AP傳輸',
  reportForm: '報表',
  exportDownload: '匯出下載',
  print: '列印',
};

export default { ...role, ...menu, ...communication, ...dictionary, ...loginLog, ...operateLog };
