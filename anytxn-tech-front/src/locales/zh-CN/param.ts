export default {
  // 接口配置参数
  incId: 'id',
  baseInfo: '基础信息',
  paramIndex: '序号',
  interfaceConfig: '接口配置参数',
  msgId: '交易码',
  status: '状态',
  clusterGroup: '集群组',
  module: '所属模块',
  createTime: '创建时间',
  updateTime: '更新时间',
  invalid: '禁用',
  effective: '启用',
  oldModel: '旧',
  newModel: '新',
  switchOpne: '开',
  switchClose: '关',
  delete: '删除',
  interfaceType: '接口类型',
  internal: '内部',
  channel: '渠道',
  // 接口映射参数
  interfaceMapping: '接口映射参数',
  createUser: '记录创建人',
  updateUser: '记录修改人',
  sourceChannel: '消息来源渠道',
  url: '指定接收方',
  protocol: '协议',
  linkMode: '链路模式',
  dualSwitch: '双发开关',
  recordSwitch: '录制开关',
  func: '功能',
  host: '地址',
  // 双方白名单服务
  doubleWhite: '双方白名单服务',
  custType: '客户鉴别类型',
  custRecogNo: '客户鉴别编号',
  // 错误代码映射参数
  errorCode: '错误代码映射参数',
  newErrCode: '新错误代码',
  errCode: '错误代码',
  errMsg: '错误消息',
  converConfig: '转换配置查找',
  converConfigUrl: '新接口地址',
  configType: '配置报文类型',
  inConfigContext: '请求配置信息',
  outConfigContext: '响应配置信息',
  // 查找配置参数
  searchConfig: '查找配置参数',
  searchConfigKey: '钥匙',
  searchConfigValue: '值',
  configDesc: '配置描述',
  // api密钥映射应用服务
  apiKeyMapping: 'api密钥映射应用服务',
  apiKey: 'api密钥',
  apiMsgId: '消息id',
  // api密钥配置应用服务
  apiKeyConfig: 'api密钥配置应用服务',
  apiSourceChannel: '源信道',
  activationTime: '激活时间',
  expirationTime: '到期时间',
  // 交易网关/出口网关（网络管理）
  cardIssuingNetManage: '网络管理',
  cardGroup: '对应卡组织前置',
  VISA: 'VISA发卡侧',
  VSACQ: 'VISA收单侧',
  signIn: '签到',
  signOut: '签退',
  startTransmission: '开始传输',
  stopTransmission: '停止传输',
  MC: '万事达发卡侧',
  MCACQ: '万事达收单侧',
  someSignIn: '部分签到',
  someSignOut: '部分签退',
  NCCC: 'NCCC发卡侧',
  NCACQ: 'NCCC收单侧',
  keyTransmission: '密钥交换',
  CARD: 'CARDPOOL发卡侧',
  CPACQF: 'CARDPOOL收单侧',
  visa: 'VISA网络管理',
  masterCard: '万事达网络管理',
  nccc: 'NCCC网络管理',
  cardpool: 'CARDPOOL网络管理',
  chanelStatus: '渠道签到状态查询',
  chanel: '通道',
  chanlState: '通讯状态',
  cardBin: '卡BIN',
  SFM: 'SFM网路管理',
  lastOperateTime: '最后操作时间',
  chanelComStatus: '管道通讯状态查询',
};
