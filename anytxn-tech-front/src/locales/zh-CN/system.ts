// 系统管理模块

// 角色管理页面（简体中文）
const role = {
  roleId: '角色ID', // 角色Id
  roleStatus: '角色状态', // 角色状态
  mountStatus: '挂载状态', // 挂载状态
  action: '操作', // 操作
  roleName: '角色名称', // 角色名称
  enable: '启用', // 启用
  unEnable: '停用', // 停用
  mounted: '已挂载', // 已挂载
  unmounted: '未挂载', // 未挂载
  roleNamePlaceholder: '请输入角色名称', // 角色名悬浮提示
  roleStatusPlaceholder: '请选择角色状态', // 角色状态悬浮提示
  mountStatusPlaceholder: '请选择挂载状态', // 挂在状态悬浮提示
  viewMount: '查看挂载详情', // 查看挂载详情
  editMount: '编辑挂载', // 编辑挂载
  tip: '提示', // 提示
  editMountSuccess: '编辑角色权限挂载成功', // 编辑角色权限挂载成功
  editSuccess: '编辑成功!', // 编辑成功!
  addSuccess: '新增成功!', // 新增成功!
  view: '查询', // 查询
  reset: '重置', // 重置
  add: '新增', // 新增
  roleDesc: '角色描述', // 角色描述
  roleIdPlaceholder: '请输入角色Id', // 请输入角色Id
  roleDescPlaceholder: '请输入角色描述', // 请输入角色描述
  limit16: '不能超过16个字', // 不能超过16个字
  limit2: '不能超过2个字', // 不能超过2个字
  limit200: '不能超过200个字', // 不能超过200个字
  role: '角色', // 角色
  copyObject: '复制对象', // 复制对象
  menuPermissions: '选单权限', // 选单权限
  viewMountDetail: '查看角色选单挂载', // 查看角色选单挂载
  editMountDetail: '编辑角色选单挂载', // 编辑角色选单挂载
  mountDetail: '角色挂载', // 角色挂载
  warningMsg: '停用角色不可编辑',
};

// 选单管理页面（简体中文）
const menu = {
  menuName: '选单名称', // 选单名称
  menuStatus: '选单状态', // 选单状态
  open: '开启', // 开启
  close: '关闭', // 关闭
  menuNamePlaceholder: '请输入选单名称', // 请输入选单名称
  menuStatusPlaceholder: '请选择状态', // 请选择状态
  sort: '排序', // 排序
  menuType: '选单类型', // 选单类型
  createTs: '创建时间', // 创建时间
  updateTs: '更新时间', // 更新时间
  menu: '选单', // 选单
  button: '按钮', // 按钮
  menuInfo: '选单信息', // 选单信息
  expanded: '展开', // 展开
  unexpanded: '折叠', // 折叠
  opFailed: '操作失败', // 操作失败
  supMenu: '上级选单', // 上级选单
  supMenuPlaceholder: '请选择上级选单', // 请选择上级选单
  menuTypePlaceholder: '请选择选单类型', // 请选择选单类型
  limit150: '不能超过150个字', // 不能超过150个字
  routeUrl: '路由地址', // 路由地址
  displaySorting: '展示排序', // 展示排序
  displaySortPlaceholder: '请输入展示排序', // 请输入展示排序
  save: '储存',
  permissionId: '权限标识',
  iconId: '图标',
  iconSelect: '图标选择',
  direction: '方向性图标',
  suggestion: '提示建议性图标',
  editor: '编辑类图标',
  data: '数据类图标',
  logo: '品牌和标识',
  other: '网站通用图标',
  messTip: '当前选择图标：',
  addIcon: '选择图标',
};

const communication = {
  passageway: '通道',
  gongneng: '功能类型',
  VSISS: 'VISA发卡侧',
  VSACQ: 'VISA收单侧',
  signIn: '签到',
  signOut: '签退',
  startTransmission: '开始传输',
  stopTransmission: '停止传输',
  MCISS: '万事达发卡侧',
  MCACQ: '万事达收单侧',
  someSignIn: '部分签到',
  someSignOut: '部分签退',
  NCISS: 'NCCC发卡侧',
  NCACQ: 'NCCC收单侧',
  keyTransmission: '密钥交换',
  CPACQS: 'CARDPOOL发卡侧',
  CPACQF: 'CARDPOOL收单侧',
  visa: 'VISA网络管理',
  masterCard: '万事达网络管理',
  nccc: 'NCCC网络管理',
  cardpool: 'CARDPOOL网络管理',
  chanelStatus: '渠道签到状态查询',
  chanel: '通道',
  communicationStatus: '通讯状态',
  cardBin: '卡BIN',
  SFM: 'SFM网路管理',
  lastOperateTime: '最后操作时间',
  chanelComStatus: '管道通讯状态查询',
};

// 字典配置页面
const dictionary = {
  dictionary: '字典资讯',
  dictId: '字典id',
  dictCode: '字典编码',
  dictName: '字典名称',
  dictDesc: '字典描述',
  status: '状态',
  dictKey: '字典值编码',
  dictValue: '字典值名称',
  parentDictKey: '父级字典值',
  dictionaryValue: '字典值',
};

// 登录日志
const loginLog = {
  loginLog: '登录日志',
  userId: '用户ID',
  userName: '用户名称',
  loginTime: '登录时间',
  loginType: '登录类型',
  loginStatus: '登录状态',
  clientIp: '登录IP',
  serverName: '登录伺服器名称',
  login: '登入',
  logout: '登出',
  fail: '失败',
  success: '成功',
};

// 操作日志
const operateLog = {
  operateLog: '操作日志',
  menuName: '选单名称',
  operateTime: '操作时间',
  operateServName: '介面名称',
  operateType: '操作类型',
  operateStatus: '操作状态',
  operateDetail: '操作详情',
  create: '新增',
  delete: '删除',
  update: '修改',
  query: '查询',
  transfer: 'AP to AP传输',
  reportForm: '报表',
  exportDownload: '汇出下载',
  print: '列印',
};

export default { ...role, ...menu, ...communication, ...dictionary, ...loginLog, ...operateLog };
