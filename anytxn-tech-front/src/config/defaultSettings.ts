import colorConstant from "@/constants/colorConstant";

const tokenSettings = {
  header: {
    colorBgHeader: '#00994e',
    colorHeaderTitle: '#fff',
    colorTextMenu: '#dfdfdf',
    colorTextMenuSecondary: '#dfdfdf',
    colorTextMenuSelected: '#fff',
    colorBgMenuItemSelected: '#00994e',
    colorTextMenuActive: 'rgba(255,255,255,0.85)',
    colorTextRightActionsItem: '#dfdfdf',
  },
  colorTextAppListIconHover: '#fff',
  colorTextAppListIcon: '#dfdfdf',
  sider: {
    colorMenuBackground: '#fff',
    colorMenuItemDivider: '#dfdfdf',
    colorBgMenuItemHover: '#00994e4d',
    colorTextMenu: '#595959',
    colorTextMenuSelected: '#242424',
    colorTextMenuActive: '#242424',
  },
};
const defaultSettings = {
  fixSiderbar: true,
  layout: 'mix',
  splitMenus: true,
  siderWidth: 240,
  colorPrimary: '#00994e',
};

export { defaultSettings, tokenSettings };
