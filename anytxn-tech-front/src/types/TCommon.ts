/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/7/1.
 */

import { ReactNode } from 'react';

/**
 * 选单子项
 */
export type TMenuItem = {
  code: string;
  parentCode?: string;
  name?: string;
  type?: string;
  url?: string;
  level: number;
  sort: number;
  children?: TMenuItem[];
};
export type TSysMenuItem = {
  key?: React.Key;
  menuId: string;
  menuName: string;
  menuType: string;
  orderSeq: string;
  menuStatus: string;
  parentMenuId?: string;
  parentMenu?: string;
  menuRouteUrl?: string;
  createUser?: string;
  updateUser?: string;
  createTs?: string;
  updateTs?: string;
  iconId?: string;
  level?: number;
  type?: string;
  children?: TSysMenuItem[];
};

/**
 * 下拉框、复选框子项
 */
export type TOptionItem = {
  key: string;
  value: string;
  label?: string | ReactNode;
  children?: [];
};

/**
 * 列表操作选项
 */
export type TAction = {
  type?: string;
  title?: string;
  disabled?: boolean;
  icon?: ReactNode | any;
  prefix?: string;
};

/**
 * 通知类型
 */
export type TNotification = 'success' | 'info' | 'warning' | 'error';

/**
 * 下拉选单子项
 */
export type TDropDownMenuItem = {
  key: string;
  label: string | ReactNode;
  icon?: ReactNode | any;
};
