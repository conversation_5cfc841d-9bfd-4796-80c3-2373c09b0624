import { defaultSettings, tokenSettings } from '@/config/defaultSettings';
import { ProLayout } from '@ant-design/pro-components';
import type { MenuDataItem } from '@ant-design/pro-layout';
import {
  getLocale,
  history,
  Link,
  Outlet,
  useLocation,
  useModel,
} from '@umijs/max';
import { ConfigProvider } from 'antd';
import en from 'antd/locale/en_US';
import zhCN from 'antd/locale/zh_CN';
import zhTW from 'antd/locale/zh_TW';
import 'dayjs/locale/en';
import 'dayjs/locale/zh-cn';
import 'dayjs/locale/zh-tw';
import _ from 'lodash';
import React, { useEffect } from 'react';
import LanguageBar from './LanguageBar';
import LogoBar from './LogoBar';
import UserBar from './UserBar';
// import defaultProps from './defaultProps';

// antd国际化枚举
const languagePackage: any = {
  en,
  'zh-cn': zhCN,
  'zh-tw': zhTW,
};

const BasicLayout: React.FC = () => {
  const locale = languagePackage[getLocale()];
  const location = useLocation();
  const { initialState } = useModel('@@initialState') as any;
  // 用于监听路由变化，若路由地址不在选单接口数据内，则跳到404页面
  useEffect(() => {
    if (_.isEmpty(initialState?.menuData)) return;
    const str = initialState?.menuData.map((item: any) => item.menuRouteUrl);
    if (!str.includes(window.location.pathname)) {
      history.push('/404');
    }
  }, [window.location.pathname]);
  /**
   * 将扁平化数据转为树形结构
   * @param flatData 扁平化数据
   * @param id id的key
   * @param parentId 父级id的key
   * @returns {Array}
   */
  const buildTree = (flatData: any[], id: string, parentId: string): any[] => {
    // 用于快速查找节点的映射
    const idMapping: Record<number, any> = {};
    flatData.forEach((node) => {
      idMapping[node[id]] = {
        ...node,
        path: node.menuRouteUrl,
        name: node.menuName,
        routes: [],
      };
    });

    let rootNodes: any[] = [];
    flatData.forEach((node) => {
      const mappedNode = idMapping[node[id]];
      if (node[parentId]) {
        // 有父节点，将其添加到父节点的 routes 中
        const parentNode = idMapping[node[parentId]];
        if (parentNode) {
          parentNode.routes?.push(mappedNode);
        }
      } else {
        // 没有父节点，这是根节点
        rootNodes.push(mappedNode);
      }
    });
    const newMenu = rootNodes.map((item) => {
      return {
        ...item,
        path: item?.routes?.[0].path,
      };
    });
    return newMenu;
  };
  // 处理选单结构
  const loopMenuItem = (menus: MenuDataItem[]) => {
    const result = menus.filter((item) => item.menuType !== '1');
    return buildTree(result, 'menuId', 'parentMenuId');
  };

  const avatarRender = () => <UserBar />;
  const actionsRender = () => <LanguageBar />;
  const logoRender = () => <LogoBar />;

  return (
    <ConfigProvider locale={locale}>
      {/* @ts-ignore */}
      <ProLayout
        token={tokenSettings}
        location={location}
        avatarProps={{ render: (props, dom) => avatarRender() }}
        actionsRender={(props) => [actionsRender()]}
        logo={logoRender()}
        title={false}
        menu={{ request: async () => loopMenuItem(initialState.menuData) }}
        menuItemRender={(menuItemProps, defaultDom) => {
          const { path, isUrl, children } = menuItemProps;
          if (isUrl || (children && children.length) || !path) {
            return defaultDom;
          }
          return <Link to={path}>{defaultDom}</Link>;
        }}
        // {...defaultProps}
        {...defaultSettings}
      >
        {/* <PageContainer
            breadcrumb={{ routes: [] }}
            onBack={() => window.history.back()}
            extra={12}
            footer={undefined}
          > */}
        <Outlet />
        {/* </PageContainer> */}
      </ProLayout>
    </ConfigProvider>
  );
};

export default BasicLayout;
