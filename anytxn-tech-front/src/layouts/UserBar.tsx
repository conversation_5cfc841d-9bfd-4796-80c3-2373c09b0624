import avatar from '@/assets/images/avatar.png';
import { userLogout } from '@/services/login';
import { ImportOutlined, UserOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Avatar, Button, Popover } from 'antd';
import { useState } from 'react';
import styles from './index.less';

export default function UserBar() {
  const [loading, setLoading] = useState(false);
  const { initialState } = useModel('@@initialState') as any;
  const { userInfo } = initialState || {};

  const getContent = () => {
    const { empName, userName, branchName } = userInfo || {};
    return (
      <div style={{ width: 250 }}>
        <div className={styles.popUser}>用户编号：{empName}</div>
        <div className={styles.popUser}>用户名：{userName}</div>
        <div className={styles.popUser}>机构名：{branchName}</div>
        <div className={styles.popLogout}>
          <Button
            loading={loading}
            icon={<ImportOutlined />}
            onClick={handleExit}
          >
            {loading ? '正在注销……' : '退出'}
          </Button>
        </div>
      </div>
    );
  };
  const handleExit = async () => {
    setLoading(true);
    const result = await userLogout();
    if (result) {
      sessionStorage.clear();
      window.location.href = '/login';
    }
  };
  return (
    <div className={styles.user}>
      <Popover placement="bottomRight" content={getContent()}>
        <Avatar
          src={avatar}
          className={styles.avatar}
          icon={<UserOutlined />}
        />
      </Popover>
    </div>
  );
}
