.logo {
  height: 4rem;
  min-height: 4rem;
  display: flex;
  align-items: center;
  padding: 0.4rem;
  padding-left: 24px;
  /* background: linear-gradient(#23496d, #001529); */
  cursor: pointer;
  fill: #fff; /* 或者使用 white */
  /* 如果是背景图片，可以使用 */
  filter: brightness(0) invert(1);
  > span {
    font-size: 1.2rem;
    font-weight: bold;
    width: 10rem;
    color: var(--gray-light);
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  background: var(--color-main);
  /* background: linear-gradient(to bottom, var(--color-main) 0%, var(--color-six) 100%); */
  box-shadow: 0 2px 8px #bfbfbf;

  > .trigger {
    padding: 0 1.2rem;
    font-size: 1.2rem;
    line-height: 64px;
    cursor: pointer;
    transition: color 0.2s;

    &:hover {
      color: var(--color-main);
    }
  }
  > .topbar {
    display: inline;
    flex: 1;
    /* align-items: center; */
    /* height: 3rem; */
    margin: 0 1rem 0 1.2rem;
    justify-content: flex-end;
    /* border: 1px solid #eeeeee; */
  }
  > .rightbar {
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    > .user {
      margin: 0 0.5rem 0 1rem;
      width: 3rem;
      cursor: pointer;
      text-align: center;

      .avatar {
        &:hover {
          background: var(--color-three);
        }
      }
    }
  }
}
.popMessage {
  width: 24rem;
  margin: var(--margin-padding-s);

  > .top {
    height: 2.2rem;
    display: flex;
    justify-content: space-between;
    border-bottom: var(--common-border);

    > .markup {
      color: var(--color-five);
      cursor: pointer;
      &:hover {
        color: var(--color-main);
      }
    }
  }
  .content {
    max-height: 20rem;
    overflow: auto;
    display: flex;
    flex-direction: column;
    > a {
      height: 2.5rem;
      line-height: 2.5rem;
    }
  }
  > .bottom {
    height: 2.8rem;
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
    border-top: var(--common-border);
  }
}
.popUser {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 2.5rem;
  color: var(--color-six);
}
.popLogout {
  height: 2.5rem;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  margin-right: 0.4rem;
  border-top: 1px dashed var(--color-two);
}

.themeBlock {
  height: 1.2rem;
  width: 1.6rem;
  margin-right: 1rem;
}

.searchMenu {
  margin: 0 0.5rem 0.5rem;
  /* margin: 0.5rem; */
  width: 14rem;
}
.iconColor {
  color: var(--gray-one);
}
.iconCollapsedOut,
.iconCollapsedIn {
  position: absolute;
  top: 40%;
  width: 1rem;
  height: 3rem;
  line-height: 3rem;
  background: var(--color-two);
  border-radius: 0 1rem 1rem 0;
  transition: all 0.2s ease-in;
}
.iconCollapsedOut {
  left: 240px;
  transition: all 0.2s ease-in;
}
.iconCollapsedIn {
  left: 80px;
  transition: all 0.2s ease-out;
}
.footer {
  text-align: center;
  padding: 0 2rem 1rem;
  font-size: 0.8rem;
}
