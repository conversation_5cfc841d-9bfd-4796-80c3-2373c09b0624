import {
  ChromeFilled,
  CrownFilled,
  SmileFilled,
  TabletFilled,
} from '@ant-design/icons';

export default {
  route: {
    path: '/',
    routes: [
      {
        path: '/',
        component: '@/layouts/BasicLayout',
        routes: [
          {
            path: '/welcome',
            name: '歡迎',
            component: '@/pages/welcome',
          },
          {
            path: '/param',
            name: '參數管理',
            routes: [
              {
                path: '/param/demo',
                name: 'demo',
                component: '@/pages/param/demo',
              },
              {
                path: '/param/interfaceConfig',
                name: '接口配置参数应用服务',
                component: '@/pages/param/interfaceConfig',
              },
              {
                path: '/param/interfaceMapping',
                name: '接口映射参数应用服务',
                component: '@/pages/param/interfaceMapping',
              },
              {
                path: '/param/doubleWhite',
                name: '双发白名单服务',
                component: '@/pages/param/doubleWhite',
              },
              {
                path: '/param/errorCodeMap',
                name: '错误代码映射参数',
                component: '@/pages/param/errorCodeMap',
              },
              {
                path: '/param/convertConfig',
                name: '转换配置查找',
                component: '@/pages/param/convertConfig',
              },
              {
                path: '/param/searchConfig',
                name: '查找配置参数',
                component: '@/pages/param/searchConfig',
              },
            ],
          },
          {
            path: '/system',
            name: '系統管理',
            routes: [
              {
                path: '/system/role',
                name: '用戶管理',
                component: '@/pages/system/role',
              },
              {
                path: '/system/menu',
                name: '選單管理',
                component: '@/pages/system/menu',
              },
            ],
          },
          {
            path: '/other',
            name: '其它',
            routes: [
              {
                path: '/other/home',
                name: '儀表盤',
                component: '@/pages/system/menu',
              },
            ],
          },
        ],
      },
    ]
  }
};