/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/6/1.
 */
import {
  OPERATE_TYPE,
  PARAMS_URLOBJ,
  SUBMIT_TYPE,
} from '@/constants/publicConstant';
import { TABLE_BIZKEY_MAP } from '@/constants/requestTableConstants';
import { IQueryData, IQueryParam } from '@/types/IRequest';
import _ from 'lodash';
import { isEmpty } from './mathUtil';

/**
 * get请求链接参数转成object对象
 * @param locationSearch
 */
export const url2obj = (locationSearch: string): object => {
  const res: any = {};
  const paramArr = locationSearch.substr(1, locationSearch.length).split('&');
  paramArr.forEach((item) => {
    const keyValue = item.split('=');
    if (keyValue.length === 2) {
      res[keyValue[0]] = keyValue[1];
    }
  });
  return res;
};

/**
 * object对象转换成get请求链接参数
 * @param obj
 */
export const obj2url = (obj: any): string => {
  if (obj) {
    if (Object.prototype.toString.call(obj) === '[object Object]') {
      let res = '';
      for (const key of Object.keys(obj)) {
        const value = obj[key];
        res += `${key}=${value}&`;
      }
      if (res.indexOf('&') > -1) {
        res = res.substr(0, res.length - 1);
      }
      return `?${res}`;
    } else {
      return `/${obj}`;
    }
  } else {
    return '';
  }
};

/**
 * 设置参数查询接口传参
 */
export const setParamQueryUrlParamData = (
  param: any,
  connector: any,
  conditionKey: string,
) => {
  const { searchValue = {}, interFaceurl, pagination = {} } = param;
  const dataD = {
    condition: Object.entries(searchValue)
      .filter(([key, value]) => value)
      .map(([key, value]) =>
        conditionKey ? `${conditionKey}=#{${key}}` : `${key}=#{${key}}`,
      )
      .join(connector || ' and '),
    data: Object.fromEntries(
      Object.entries(searchValue).filter(([key, value]) => value),
    ),
  };
  const { TABLE_NAME } = interFaceurl;
  // 表名改为在REQUEST_TABLE_MAP维护，可在param传
  const tableName = TABLE_NAME || '';
  const { currentPage: pageNo, pageSize } = pagination;
  // 判断有无传分页
  const otherParam = isEmpty(pagination) ? {} : { pageNo, pageSize };
  return {
    body: {
      ...otherParam,
      ...dataD,
      tableName,
    },
  };
};

/**
 * 设置参数维护接口传参
 */
export const setParamUpdateUrlParamData = (params: any) => {
  const { interFaceurl = PARAMS_URLOBJ.edit, data, type } = params;
  const { TABLE_NAME } = interFaceurl;
  const deleteArr = [
    'incId',
    'paramIndex',
    'type',
    'createUser',
    'createTime',
    'updateTime',
    'updateUser',
    'createUserNo',
    'updateUserNo',
    'syncTime',
    'createOrgNo',
    'updateOrgNo',
  ];
  // 兼容单表，多表传数组
  const postParam = Array.isArray(data) ? data : [{ data }];
  const paramList = postParam.map((param) => {
    const { data: paramData, bizKey = [], tableName } = param;
    // 删除不需要的参数
    deleteArr.map(
      (item) => !_.isNil(paramData?.[item]) && delete paramData[item],
    );
    // 多表的需要单独传，否则使用外层的TABLE_NAME
    const tName = tableName || TABLE_NAME;
    const paramDataList = Array.isArray(paramData) ? paramData : [paramData];
    return {
      tableName: tName,
      // bizKey改为在TABLE_BIZKEY_MAP维护，可在param传
      bizKey: TABLE_BIZKEY_MAP[tName] || bizKey,
      paramData: paramDataList.map((paramItem) => {
        // 如果多表里存在可编辑表格可新增可修改的情况，可以每行数据传_opType_，没传用外层的type
        const operateType =
          paramItem._opType_ === SUBMIT_TYPE.create
            ? OPERATE_TYPE.create
            : type;
        // 参数新增，参数状态默认为有效
        const otherParam = [OPERATE_TYPE.create, OPERATE_TYPE.copy].includes(
          operateType,
        )
          ? { paramSts: '1' }
          : {};
        // 删除不需要传的参数
        deleteArr.map(
          (item) => !_.isNil(paramItem?.[item]) && delete paramItem[item],
        );
        return {
          // 默认版本1
          version: 1,
          _opType_: SUBMIT_TYPE[type] || SUBMIT_TYPE.edit,
          ...paramItem,
          ...otherParam,
        };
      }),
    };
  });

  return {
    body: {
      tableName: TABLE_NAME,
      paramList,
    },
  };
};

/**
 * 设置业务查询接口传参
 * @param params
 */
export const setBusinessQueryParamData = (params: IQueryParam) => {
  const { searchValue = {}, pagination = {}, interFaceurl } = params;
  const { currentPage: pageNo, pageSize } = pagination as any;
  const otherParam = isEmpty(pagination) ? {} : { pageNo, pageSize };
  const newSearchValue: any = {};
  for (const key in searchValue) {
    if (
      searchValue.hasOwnProperty(key) &&
      searchValue[key] !== null &&
      searchValue[key] !== ''
    ) {
      newSearchValue[key] = searchValue[key];
    }
  }
  
  return {
    // header: {
    //   gid: '12312',
    //   srcSystem: 'ANYTECH',
    //   path: '/' + interFaceurl,
    //   msgId: '/' + interFaceurl,
    // },
    body: {
      ...newSearchValue,
      page: {
        ...otherParam,
      },
    },
  };
};

/**
 * 处理返回数据格式
 */
export const setResult = (res: any): IQueryData => {
  return Array.isArray(res?.data)
    ? {
        data: res?.data.map((item: any, i: number) => ({
          ...item,
          paramIndex: i++ + 1, // 用于页面序号展示
        })),
        total: res?.page?.totalCount,
      }
    : { data: res?.data ?? [], total: 0 };
};
