import * as publicConstant from '@/constants/publicConstant';
import { TOptionItem } from '@/types/TCommon';
import { FormattedMessage } from '@umijs/max';
import dayjs from 'dayjs';
import _ from 'lodash';
import { formatNumber } from './mathUtil';

/**
 * 获取 antd table 高度
 * @param diff 误差，偏低则给正数，偏高则给负数
 * @returns {number}
 */
export function getTableHeight(diff: number = 0): number {
  // header=64, footer=64 appModule=64, margin=16*4, tabbleHeader=48, pagination=56 (没有footer)
  return (
    document.documentElement.scrollHeight - 64 - 64 - 16 * 4 - 48 - 56 + diff
  );
}

/**
 * 获取表格操列宽度
 * @param count 操作按钮数量
 * @returns {number}
 */
export function getActionColumnWidth(count: number = 1): number {
  let width = 100;
  switch (count) {
    case 1:
      width = 60;
      break;
    case 2:
      width = 96;
      break;
    case 3:
      width = 136;
      break;
    case 4:
      width = 188;
      break;
    case 5:
      width = 232;
      break;
    case 6:
      width = 280;
      break;
  }
  return width;
}

// /**
//  * 获取页面普通增删改查链接地址
//  * @param prefix
//  * @param apiName
//  * @returns {{}}
//  */
// export function getPageApi (prefix: string, apiName: string): object {
//   return {
//     common: `${prefix}${apiName}/`,
//     list: `${prefix}${apiName}/list`,
//     detail: `${prefix}${apiName}/id`,
//     create: `${prefix}${apiName}/create`,
//     update: `${prefix}${apiName}/update`,
//     delete: `${prefix}${apiName}/delete`
//   }
// }

/**
 * 渲染表格日期字段
 * @param text
 * @param format
 * @returns {string|string}
 */
export function renderDateField(
  text: string,
  format: string = publicConstant.DATE_FORMATE,
): string {
  return text ? dayjs(text).format(format) : publicConstant.NODATA;
}

/**
 * 渲染表格枚举值
 * @param text
 * @param options [{key,value}]
 * @returns {string}
 */
export function renderFieldByOptions(
  text: string,
  options: Array<TOptionItem>,
): string {
  let res: string | undefined = publicConstant.NODATA;
  options.forEach((item) => {
    if (item.key === text) {
      res = item.value; // 有可能undefined
    }
  });
  return res;
}

/**
 * 返回用户状态显示标签对象（后续需要放在useIntlCustom里）
 * @returns {{"0": JSX.Element, "1": JSX.Element, "-1": JSX.Element}}
 */
// export function renderUserStatus(): Object {
//   // 硬编码，如果 USERSTATUS_NAME 有增加修改，这里需要同步更新
//   return {
//     '-1': (
//       <Tag color="error" icon={<StopOutlined />}>
//         {publicConstant.USERSTATUS_NAME['-1']}
//       </Tag>
//     ),
//     0: (
//       <Tag color="processing" icon={<ClockCircleOutlined />}>
//         {publicConstant.USERSTATUS_NAME['0']}
//       </Tag>
//     ),
//     1: (
//       <Tag color="success" icon={<SafetyOutlined />}>
//         {publicConstant.USERSTATUS_NAME['1']}
//       </Tag>
//     ),
//   };
// }

/**
 * 扁平化对象
 * @param obj 对象
 * @param parentKey 父级key
 * @param result 上一次遍历的结果
 * @returns {object}
 */
export const flattenObject = (
  obj: object,
  parentKey = '',
  result = {},
): object => {
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      let newKey = parentKey ? `${parentKey}.${key}` : key;
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        flattenObject(obj[key], newKey, result);
      } else {
        result[newKey] = obj[key];
      }
    }
  }
  return result;
};

/**
 * 嵌套对象扁平化
 * @param obj
 * @returns {object} 模块扁平化对象
 */
export const flattenNestedObject = (obj: object): object => {
  let res = {};
  for (const key in obj) {
    if (obj.hasOwnProperty(key) && typeof obj[key] === 'object') {
      const temp = flattenObject({ [key]: obj[key] });
      res = { ...res, ...temp };
    }
  }
  return res;
};

/**
 * 找出目录下符合规则的文件
 * @param context 上下文
 * @param reg 匹配文件的正则
 * @param flatten 输出对象是否需要扁平化 {模块1: {字段1，字段2}, 模块2: {字段3, 字段4}}
 * @returns {object}
 */
export const getFileObj = (
  context: any,
  reg: RegExp,
  flatten: boolean = true,
): object => {
  const obj: object = {};
  const temp = context.keys().filter((t: string) => t !== './index.ts');

  temp.forEach((key: string) => {
    const match = key.match(reg);
    if (match) {
      const fileName = match[1];
      obj[fileName] = context(key).default;
    }
  });
  return flatten ? flattenNestedObject(obj) : obj;
};
/**
 * 根据传入的rem转换为px某些组件如G6需要使用px）
 * @param rem
 * @returns {string}
 */
export const getFontSize = (rem) => {
  const { fontSize } = window.getComputedStyle(document.documentElement);
  const matchFontSize = fontSize.match(/\d+(?=px)/);
  let result = 0;
  if (matchFontSize) {
    result = Number(matchFontSize[0]);
  }
  const matchRem = rem.match(/\d+(?=rem)/);
  let remNum = 0;
  if (matchRem) {
    remNum = Number(matchRem[0]);
  }
  return result * remNum;
};

/**
 * 动态修改表单某个属性
 * @param infoFormConfig
 * @param fieldName
 * @param newDisabledValue
 */
export const updateDisabledProperty = (
  infoFormConfig,
  fieldName,
  newDisabledValue,
) => {
  const newFieldName = _.isArray(fieldName) ? fieldName : fieldName.split(',');
  _.forEach(infoFormConfig, (section) => {
    if (section.type === 'Row') {
      _.forEach(section.data, (field) => {
        if (newFieldName.includes(field.name)) {
          field.disabled = newDisabledValue;
        }
      });
    }
  });
};

// 格式化html中的文本
const formateHtmlText = (prefix, key) => {
  const id = prefix ? `${prefix}.${key}` : key;
  return <FormattedMessage id={id} />;
};

const renderCol = (
  text: string,
  list: any,
  prefix: string = '',
  showKey = true,
) => {
  const label = _.get(_.find(list, { key: text }), 'value', '');
  if (!label) {
    return text;
  }
  if (showKey) {
    return (
      <>
        <span>{text} - </span>
        {formateHtmlText(prefix, label)}
      </>
    );
  } else {
    return formateHtmlText(prefix, label);
  }
};

/**
 * 格式化值
 * @param value 值
 * @param option 相关配置（如valueType、prefix...）
 * @returns {string | JSX}
 */
export const renderValueByType = (value, option) => {
  const {
    valueType,
    dictType,
    data,
    prefix,
    showKey,
    amountLength,
    optionPrefix,
  } = option;
  switch (valueType) {
    // 金额
    case publicConstant.RENDER_TYPE.Amount:
      return formatNumber(value, amountLength);
    // 数据字典
    case publicConstant.RENDER_TYPE.Dictionary: {
      // const [dictState] = store.useModel('dict');
      // const list: any = dictState.dictMap[dictType] || [];
      // return renderCol(value, list, prefix, showKey);
    }
    // 数据字典(mock)
    case publicConstant.RENDER_TYPE.MockDictionary:
      return renderCol(value, data, optionPrefix || prefix, showKey);
    // 时间
    case publicConstant.RENDER_TYPE.DateTime:
      return dayjs(value).format(publicConstant.TIME_FORMATE);
    // 日期
    case publicConstant.RENDER_TYPE.Date:
      return dayjs(value).format(publicConstant.DATE_FORMATE);
    default:
      return value;
  }
};

/**
 * 格式化列表值
 * @param column 每列列表数据
 * @returns {object}
 */
export const renderColumnByType = (column) => {
  let { valueType, render } = column;
  const res = { ...column };
  switch (valueType) {
    // 超长省略
    case publicConstant.RENDER_TYPE.Ellipsis:
      res.ellipsis = true;
      break;
    // 金额
    case publicConstant.RENDER_TYPE.Amount:
      res.align = 'right';
    // 其他不需特殊加字段的类型（数据字典、数据字典(mock)、时间、日期...）
    default:
      // 有render方法则自己处理
      if (_.isNil(render)) {
        res.render = (text) => renderValueByType(text, column);
      }
      break;
  }
  delete res.data;
  return res;
};
/**
 * 是否请求列表接口，查询条件若有必输，首次进页面则不掉用接口
 * @returns
 */
export const isGetTableData = (data: any) => {
  const { searchSource } = data;
  let isSearch;
  searchSource.filter(
    (item) =>
      (isSearch =
        item.rules && item.rules.some((item) => item.required === true)),
  );
  return isSearch;
};
/**
 * 格式化form时间
 * @param type
 * @param value
 * @returns
 */
export const formatformItemData = (type, value) => {
  if (type === publicConstant.COMPONENT_TYPE.DATE_PICKER) {
    return value && dayjs(value);
  } else {
    return value;
  }
};
/**
 * 处理form录入的值，时间格式或者输入框去掉空格
 * @param type
 * @param value
 * @param format
 * @returns
 */
export const formatformItemNormalize = (
  type,
  value,
  format = publicConstant.DATE_FORMATE,
) => {
  let res;
  switch (type) {
    case publicConstant.COMPONENT_TYPE.DATE_PICKER:
      res = value && dayjs(value).format(format);
      break;
    case publicConstant.COMPONENT_TYPE.INPUT:
      res = _.isNumber(value) ? value : value?.trim();
      break;
    default:
      res = value;
      break;
  }
  return res;
};
// 金额类输入框默认 13位整数，2位小数
export const formatterAmountInput = (value, decimal) => {
  // 使用Intl.NumberFormat来格式化数字为带千分位的字符串
  // 注意：这里使用'zh-Hans-CN'作为locale，但'zh-CN'通常也是有效的
  const formatter = new Intl.NumberFormat('zh-Hans-CN', {
    style: 'decimal',
    minimumFractionDigits: decimal, // 根据需要设置最小小数位数
    maximumFractionDigits: decimal, // 根据需要设置最大小数位数
  });
  return value && formatter.format(value);
};
// 处理金额数据
export const parserAmountInput = (value) => {
  // 移除所有非数字字符（包括千分位逗号和小数点），然后尝试转换
  // 但这里我们不应该移除小数点，所以只移除千分位逗号
  const cleanedValue = value.replace(/,/g, '');
  // 尝试将清理后的字符串转换为浮点数
  const numericValue = parseFloat(cleanedValue);
  // 如果转换后的数字不是NaN且是有限的（避免Infinity等情况），则返回它
  return !isNaN(numericValue) && isFinite(numericValue)
    ? numericValue
    : undefined;
};
/**
 * 分页国际化
 * @param total 总页数
 * @returns {string}
 */
export const showTotal = (total) => {
  // const locale = localStorage.getItem('locale');
  // switch (locale) {
  //   case LANGUAGE_LIST.EN.locale:
  //     return `Toal ${total}`;
  //   case LANGUAGE_LIST.TW.locale:
  //     return `共 ${total} 條`;
  //   default:
  //     return `共 ${total} 条`;
  // }
};
