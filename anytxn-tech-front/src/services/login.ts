import { dataRequest } from '@/common/request';

export const getUserInfo = async () => {
  return await dataRequest.post('user/getUserInfo')
}
export const getUserPermisson = async () => {
  return await dataRequest.post('user/getUserPermission')
}
export const userLogin = async (params: any) => {
  return await dataRequest.post('login', {
    data: { ...params },
  });
};
export const userLogout = async (params?: any) => {
  return await dataRequest.post('logout', {
    data: { ...params },
  });
};

export const getSysMenuData = async (params?: any) => {
  return await dataRequest.get('sysMenuData', {
    params: { ...params },
  });
};
