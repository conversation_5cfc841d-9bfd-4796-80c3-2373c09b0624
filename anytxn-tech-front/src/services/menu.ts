import { dataRequest } from '@/common/request';
import { TSysMenuItem } from '@/types/TCommon';

const processMenuData = (menuData: TSysMenuItem[]) => {
  // 递归方法
  const recursionMenuData = (item: TSysMenuItem, childrenData: any) => {
    childrenData &&
      childrenData.forEach((child: TSysMenuItem) => {
        if (item.menuId === child.parentMenuId) {
          recursionMenuData(child, childrenData);
          item.children.push(child);
        }
      });
    if (item.children && item.children.length == 0) {
      delete item.children;
    }
  };
  const menuTree: TSysMenuItem[] = [];
  const childrenData: TSysMenuItem[] = [];
  menuData?.forEach((item) => {
    item.children = [];
    item.key = item.menuId;
    // 数据转换
    if (item.level === 1) {
      menuTree.push(item);
    } else {
      childrenData.push(item);
    }
  });
  menuTree.forEach((item) => recursionMenuData(item, childrenData));
  // console.log(menuTree);
  return menuTree;
  // setMenuList(JSON.parse(JSON.stringify(menuTree)));
};

// 数据处理
const handleData = (res: any) => {
  const aaa = res?.menuInfoList || res
  // 数据处理，增加level和type的数据处理
  aaa?.forEach((item: any) => {
    // menuStatus和iconId处理,需要注释
    if (!item.menuStatus) {
      item.menuStatus = 'Y';
    }
    // if (!item.iconId) {
    //   item.iconId = item.icon ? item.icon : 'SettingOutlined';
    //   delete item.icon;
    // }
    // type处理
    if (
      item.menuRouteUrl &&
      item.menuRouteUrl.length > 0 &&
      item.menuRouteUrl !== ' '
    ) {
      item.type = 'link';
    } else {
      item.type = 'directory';
    }
    // level处理
    const strLength = item.menuId.toString().length;
    switch (strLength) {
      case 1:
        item.level = 1;
        break;
      case 4:
        item.level = 2;
        break;
      case 7:
        item.level = 3;
        break;
      case 10:
        item.level = 4;
        break;
      case 13:
        item.level = 5;
        break;
      default:
        item.level = 5;
    }
  });
  return aaa;
};

// 选单管理页面获取平台所有选单按钮数据，树形结构
export async function getTreeList(data: object): Promise<any> {
  // const res = await dataRequest.post('sysMenuData', data);
  const param = { body: data };
  const res = await dataRequest.post('menu/list', { data: { ...param } });
  // 数据处理封装
  const handleRes = handleData(res.data.menuInfoList);
  // 扁平化数据改成树状数据
  const val = processMenuData(handleRes);
  return val;
}

// 选单管理页面获取平台所有选单按钮数据，扁平化数据
export async function getMenuInfo(data: object): Promise<any> {
  const param = { body: data };
  const res = await await dataRequest.post('menu/list', { data: param });
  // 数据处理封装
  const handleRes = handleData(res.data.menuInfoList);
  return handleRes;
}

// 根据角色id获取当前角色选单数据
export async function getMountDataById(val: object): Promise<any> {
  const res = await dataRequest.post('rolePermission/select', { data: val });
  const { data } = res;
  return data;
}

export async function getRoleListData(data: object): Promise<any> {
  const res = await dataRequest.post('role/getRoleInfo', { data });
  return res?.data;
}

export async function updateData(data: object): Promise<any> {
  return await dataRequest.post('menu/update', { data });
}

// 获取用户权限
export async function getUserPermission(
  data: object,
): Promise<{ menuData: TSysMenuItem[]; buttonData: TSysMenuItem[] }> {
  const res = dataRequest.post('user/getUserPermission', { data });
  const val = handleData(res?.data);
  let result = val?.map((item: TSysMenuItem) => {
    // 若menuRouteUrl为'',type = 'directory',移除menuRouteUrlw
    if (item.type === 'directory' && item.menuRouteUrl === ' ') {
      delete item.menuRouteUrl;
    }
    return item;
  });
  const menuData: TSysMenuItem[] = [];
  const buttonData: TSysMenuItem[] = [];
  result?.forEach((item: TSysMenuItem) => {
    // 有权限的按钮数据
    item.menuType === '1' && buttonData.push(item);
    // 有权限的选单数据
    item.menuType === '0' && menuData.push(item);
  });
  return { menuData, buttonData };
}

// 获取用户基本信息
export const getUserInfo = async () => {
  return await dataRequest.get('api/userInfo');
};

export default {
  getTreeList,
  getRoleListData,
  updateData,
  getUserPermission,
  getMenuInfo,
  getMountDataById,
};
