import { dataRequest } from '@/common/request';
import { PARAMS_URLOBJ, SUCCESS_CODE } from '@/constants/publicConstant';
import { IQueryData, IQueryParam } from '@/types/IRequest';
import {
  setBusinessQueryParamData,
  setParamQueryUrlParamData,
  setParamUpdateUrlParamData,
  setResult,
} from '@/utils/urlUtil';

// 获取字典
const getDictionaryData = async (data: any) =>
  await dataRequest.post('/api/dictionary', { data });

// 参数列表分页查询(connector:连接符，默认and,可传参覆盖；conditionKey：默认为searchValue对象属性名，可选择传参覆盖)
const getTableListData = async (
  param: any,
  connector = '',
  conditionKey = '',
): Promise<IQueryData> => {
  const params: any = setParamQueryUrlParamData(param, connector, conditionKey);
  const { URL } = param.interFaceurl;
  const res = await dataRequest.post(URL, params);
  return setResult(res);
};

// 参数：新增，编辑，删除
const getEditPost = async (params: any) => {
  const postParams = setParamUpdateUrlParamData(params);
  const { URL } = params.interFaceurl;

  // 检查参数接口通过才调维护接口
  const checkRes = await dataRequest.post(PARAMS_URLOBJ.chekc, {
    data: postParams,
  });
  if (checkRes.header?.errorCode === SUCCESS_CODE) {
    const res = await dataRequest.post(URL, { data: postParams });
    return res ? { ...res } : false;
  }
  return false;
};

// 列表分页查询
const getTableListDataBiz = async (
  params: IQueryParam,
): Promise<IQueryData> => {
  const reqData = setBusinessQueryParamData(params);
  const res = await dataRequest.post(params.interFaceurl, { data: reqData });
  return setResult(res);
};

// 新增，编辑
const getEditPostBiz = async (params: any) => {

  const newParams = {
    body: { ...params },
  };
  delete newParams.body.interFaceurl;
  delete newParams.body?.type;
  const res = await dataRequest.post(params.interFaceurl, { data: newParams });
  return res ? { ...res } : false;
};

// 删除
const getDeletePostBiz = async (params: any) => {
  const newParams = {
    body: { ...params },
  };
  delete newParams.body.interFaceurl;
  delete newParams.body?.type;
  const res = await dataRequest.post(params.interFaceurl, { data: newParams });
  return res ? { ...res } : false;
};

export default {
  getDictionaryData,
  getTableListData,
  getEditPost,
  getTableListDataBiz,
  getEditPostBiz,
  getDeletePostBiz,
};
