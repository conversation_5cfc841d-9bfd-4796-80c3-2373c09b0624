// 运行时配置
// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate
import { getUserPermisson, getUserInfo } from '@/services/login';
import _ from 'lodash';
import { SESSION } from './constants/publicConstant';

export async function getInitialState(): Promise<object> {
  let userInfo: any;
  let menuData: any;
  const tokenVal = sessionStorage.getItem(SESSION.token);
  if (!_.isEmpty(tokenVal)) {
    userInfo = await getUserInfo();
    menuData = await getUserPermisson();
  } else {
    userInfo = {};
    menuData = {};
  }
  return { userInfo: userInfo?.data, menuData: menuData?.data };
}

// export const layout = () => {
//   return {
//     logo: 'https://img.alicdn.com/tfs/TB1YHEpwUT1gK0jSZFhXXaAtVXa-28-27.svg',
//     menu: {
//       locale: false,
//     },
//   };
// };
