export default {
  mainColors: {
    success: '#3adb76',
    secondary: window.localStorage.skin === 'dark' ? '#74C2E1' : '#2B4162',
    primary: '#1779ba',
    warning: '#ffae00',
    alert: '#cc4b37',
    bg: window.localStorage.skin === 'dark' ? '#2B4162' : '#fefefe',
    disabled: '#555555',
  },
  basicColors: {
    lightGray: '#e6e6e6',
    mediumGray: '#cacaca',
    darkGray: '#8a8a8a',
    black: '#0a0a0a',
    white: '#fefefe',
    highLight: '#74C2E1',
  },
  bodyFontColor: window.localStorage.skin === 'dark' ? '#e6e6e6' : '#0a0a0a',
  levelColors: ['#cc4b37', '#ff9600', '#fff007', '#1779ba', '#3adb76'],
  blueColors: ['#01578B', '#0288D1', '#03A9F4', '#4FC3F7', '#B3E5FC', '#E1F5FE'],
  normalPieColors: ['#08ade7', '#50f0c4', '#2b4c5f', '#90aacf', '#ff7a11'],
  chartColors: ['#38CDDC', '#fed44d', '#4CAF50'],
  rateChartColors: ['#38CDDC', window.localStorage.skin === 'dark' ? '#555555' : '#cacaca'],
  hybridChartColors: [
    '#4CAF50',
    '#009688',
    '#FFC107',
    '#C093C9',
    '#FF9800',
    '#CDDC39',
    '#795548',
    '#E91E63',
    '#FFEB3B',
    '#558B2F',
    '#37474F',
    '#FF8F00',
    '#F47D81',
    '#27AE60',
    '#8E44AD',
    '#E67E22',
    '#D2527F',
    '#16A085',
    '#E26A6A',
    '#ECF0F1',
    '#FF7A5A',
    '#FFB85F',
    '#f24a4a',
    '#e1dda1',
    '#a1bd93',
    '#f42448',
    '#fed44d',
  ],
  mapWarnLevel: ['rgba(120, 195, 150, 1)', 'rgba(230, 200, 151, 1)', 'rgba(230, 136, 150, 1)'],
  fontSize: 12,
  titleSize: 14,
};
