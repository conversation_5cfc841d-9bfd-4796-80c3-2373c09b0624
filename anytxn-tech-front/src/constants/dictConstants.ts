import urlConstants from './urlConstants';

// 字段或参数枚举
const DICT_TYPE = {
  dict: 'dict',
  param: 'param',
};

const DICT_CONSTANTS = {
  // 页面用得较多的需要取字典或参数的字段配置
  DICT_ENUM_MAP: {
    crcdOrgNo: {
      type: DICT_TYPE.dict,
      // 对应的service名
      url: urlConstants.BASE_ORG_PARAM.LIST,
      // select的option转换的key和value，需要的拼接的再加上showKey
      optionKey: 'crcdOrgNo',
      optionValue: 'crcdOrgNo',
    },
  },

  // 状态枚举
  PARAM_STATUS: [
    { key: 'Y', value: 'effective' },
    { key: 'N', value: 'invalid' },
  ],

  // 开关
  COMMON_SWITCH: [
    { key: '0', value: 'switchOpne' },
    { key: '1', value: 'switchClose' },
  ],

  // 链路模式
  LIKE_MODE: [
    { key: '1', value: 'oldModel' },
    { key: '2', value: 'newModel' },
  ],

  // 接口类型
  INTER_FACE_TYPE: [
    { key: '0', value: 'internal' },
    { key: '1', value: 'channel' },
  ],

  // 分页可选条数
  PAGEOPTIONS: [
    { label: 'page_10', value: 10 },
    { label: 'page_20', value: 20 },
    { label: 'page_50', value: 50 },
    { label: 'page_100', value: 100 },
  ],
  // VISA發卡側、VISA收單側；
  VISA_PASSAGE_WAY: [
    { key: 'VISA', value: 'VISA' },
    { key: 'VSACQ', value: 'VSACQ' },
  ],
  // 1-簽到、2-簽退、6-開始傳輸、7-終止傳輸
  VISA_GONGG_NENEG: [
    { key: '1', value: 'signIn' },
    { key: '2', value: 'signOut' },
  ],
  //  頁面要素中「通道」的枚舉值為VISA發卡側、MC-萬事達發卡側、NCCC-NCCC發卡側、CARD-萬事發卡側；
  WSD_PASSAGE_WAY: [
    { key: 'VISA', value: 'VISA' },
    { key: 'MC', value: 'MC' },
    { key: 'NCCC', value: 'NCCC' },
    { key: 'CARD', value: 'CARD' },
  ],
  // 頁面要素中「功能類型」的枚舉值為1-簽到、2-簽退、3-秘鑰交換、4-部分簽到、5-部分簽退。
  WSD_GONGG_NENEG: [
    { key: '1', value: 'signIn' },
    { key: '2', value: 'signOut' },
    { key: '3', value: 'keyTransmission' },
    { key: '4', value: 'someSignIn' },
    { key: '5', value: 'someSignOut' },
    { key: '6', value: 'startTransmission' },
    { key: '7', value: 'stopTransmission' },
  ],
  //  NCCC-NCCC發卡側、NCACQ-NCCC收單側；
  NCCC_PASSAGE_WAY: [
    { key: 'NCCC', value: 'NCCC' },
    { key: 'NCACQ', value: 'NCACQ' },
  ],
  // 1-簽到、2-簽退、3-秘鑰交換。
  NCCC_GONGG_NENEG: [
    { key: '1', value: 'signIn' },
    { key: '2', value: 'signOut' },
    { key: '3', value: 'keyTransmission' },
  ],
  // CPACQ-CARDPOOL發卡側、CPACQ-CARDPOOL收單側
  CARD_PASSAGE_WAY: [
    { key: 'CPACQ', value: 'CARD' },
    { key: 'CPACQ', value: 'CPACQF' },
  ],
  // 1-簽到、2-簽退
  CARD_GONGG_NENEG: [
    { key: '1', value: 'signIn' },
    { key: '2', value: 'signOut' },
  ],
  // SFM網路管理-功能類型
  SFM_GONGG_NENEG: [
    { key: '1', value: 'signIn' },
    { key: '2', value: 'signOut' },
  ],
  // 协议
  PROTOCOL: [
    { key: 'http', value: 'http' },
    { key: 'https', value: 'https' },
    { key: 'tcp', value: 'tcp' },
  ],
  // 选单状态枚举
  MENU_STATUS: [
    { key: 'Y', value: 'open' },
    { key: 'N', value: 'close' },
  ],
  // 选单类型枚举
  MENU_TYPE: [
    { key: '1', value: 'button' },
    { key: '0', value: 'menu' },
  ],
  // 角色状态枚举
  ROLE_STATUS_EUNM: [
    { key: 'Y', value: 'enable' },
    { key: 'N', value: 'unEnable' },
  ],
  // 挂载状态枚举
  MOUNT_STATUS_EUNM: [
    { key: 'Y', value: 'mounted' },
    { key: 'N', value: 'unmounted' },
  ],
};
export default DICT_CONSTANTS;
