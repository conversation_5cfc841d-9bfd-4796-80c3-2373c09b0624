const prefix = 'api';
export const getPageApi = (prefix: string, apiName: string) => {
  return {
    common: `${prefix}${apiName}/`,
    list: `${prefix}${apiName}/list`,
    detail: `${prefix}${apiName}/detail`,
    create: `${prefix}${apiName}/create`,
    update: `${prefix}${apiName}/update`,
    delete: `${prefix}${apiName}/delete`,
  };
};
// system
export const ORGANIZATION_API = {
  list: `${prefix}/organization/list`,
};
export const USER_API = {
  list: `${prefix}/user/list`,
  detail: `${prefix}/user/detail`,
  create: `${prefix}/user/create`,
  update: `${prefix}/user/update`,
  delete: `${prefix}/user/delete`,
  roleList: `${prefix}/user/roleList`,
  updateRole: `${prefix}/user/updateRole`,
};
export const ROLE_API = {
  list: `${prefix}/role/list`,
  detail: `${prefix}/role/detail`,
  create: `${prefix}/role/create`,
  update: `${prefix}/role/update`,
  delete: `${prefix}/role/delete`,
  menuList: `${prefix}/role/menuList`,
  updateMenu: `${prefix}/role/updateMenu`,
  userList: `${prefix}/role/userList`,
  updateUser: `${prefix}/role/updateUser`,
};
export const MENU_API = {
  list: `${prefix}/menu/list`,
  detail: `${prefix}/menu/detail`,
  create: `${prefix}/menu/create`,
  update: `${prefix}/menu/update`,
  delete: `${prefix}/menu/delete`,
};
