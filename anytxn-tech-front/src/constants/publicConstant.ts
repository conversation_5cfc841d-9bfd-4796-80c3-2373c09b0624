// 展示常量
export const PAGE_SIZE = 10;
export const PAGE_SIZE_OPTIONS: Array<number> = [10, 20, 50];
export const NODATA = '- -';
export const MONTH_FORMATE = 'YYYY-MM';
export const DATE_FORMATE = 'YYYY-MM-DD';
export const DATE_FORMATE_SECONG = 'YYYYMMDDHHmmss';
export const TIME_FORMATE = 'YYYY-MM-DD HH:mm:ss';
export const MENU_THEMEN = 'dark'; // 头部和左侧选单主题，实际被颜色主题覆盖
export const SUCCESS_CODE = '000000'; // 相应成功码
// 选单类型枚举
export const MENUTYPE_KEY = {
  directory: 'directory',
  link: 'link',
  blank: 'blank',
  api: 'api',
};
export const MENUTYPE_COLOR = {
  directory: '#87d068',
  link: '#2db7f5',
  blank: '#108ee9',
  api: '#ffc53d',
};
// 操作类型
export const OPERATE_TYPE: any = {
  create: 'create',
  detail: 'detail',
  copy: 'copy',
  edit: 'edit',
  delete: 'delete',
  list: 'list',
  cancel: 'cancel',
  save: 'save',
  up: 'up',
  down: 'down',
  submit: 'submit',
};
// sessionStorage枚举
export const SESSION = {
  token: 'token',
  codeIndex: 'codeIndex',
  userInfo: 'userInfo',
  menuData: 'menuData',
  menuTree: 'menuTree',
  menuSelected: 'menuSelected',
};
// localStorage枚举
export const LOCAL = {
  LOCALE: 'locale',
  THEME: 'theme',
};
// 语言枚举
export const LANGUAGE_LIST: any = [
  { label: '繁體中文', key: 'zh-TW' },
  { label: '简体中文', key: 'zh-CN' },
  { label: 'English', key: 'en-US' },
];
// 常用组件名称枚举
export const COMPONENT_TYPE = {
  // antd 组件
  INPUT: 'Input',
  EXTARA_BTN_INPUT: 'ExtarBtnInput',
  INTERVAL_INPUT: 'IntervalInput',
  INPUT_NUMBER: 'InputNumber',
  AMOUNT_INPUT: 'AmountInput',
  CHECK_BOX: 'Checkbox',
  SELECT: 'Select',
  RADIO: 'Radio',
  TREE: 'Tree',
  TREE_SELECT: 'TreeSelect',
  CASCADER: 'Cascader',
  DATE_PICKER: 'DatePicker',
  RANGE_PICKER: 'RangePicker',
  SWITCH: 'Switch',
  RATE: 'Rate',
  SLIDER: 'Slider',
  UPLOAD: 'Upload',
  // LayoutTemplate 子组件
  TABLE: 'Table',
  FORM: 'Form',
  EDITTABLE: 'EditTable',
  TEXTAREA: 'TextArea',
  DROPDOWN: 'Dropdown',
  SEARCH: 'Search',
};
// 渲染需要二次处理的类型
export const RENDER_TYPE = {
  Amount: 'Amount',
  Dictionary: 'Dictionary',
  MockDictionary: 'MockDictionary',
  Ellipsis: 'ellipsis',
  DateTime: 'DateTime',
  Date: 'Date',
};
// 列表子项类型
export const FORMITEM_TYPE = {
  FormHearder: 'FormHearder',
  Single: 'Single',
  Row: 'Row',
  List: 'List',
  CommonTable: 'CommonTable',
  EditTable: 'EditTable',
  Calendar: 'Calendar',
};
// 通知类型
export const NOTIFICATION_TYPE = {
  SUCCESS: 'success',
  INFO: 'info',
  ERROR: 'error',
  WARNING: 'warning',
};
// 表格类型
export const TABLE_TYPE = {
  CommonTable: 'CommonTable',
  TreeTable: 'TreeTable',
};
// 提交表单类型
export const SUBMIT_TYPE: any = {
  create: 'A',
  edit: 'U',
  delete: 'U',
  copy: 'A',
};

// 提交表单类型
export const NOTIFICATION_PROMPT = {
  create: 'createFailed',
  edit: 'editFailed',
  delete: 'deleteFailed',
};

// dayjs日期类型
export const DATE_TYPE = {
  day: 'day',
  week: 'week',
  month: 'month',
  year: 'year',
};

// 参数接口增删改差名称（临时使用，后期后管出接口了在拿掉）
export const PARAMS_URLOBJ = {
  list: 'nq/param/pageQuery',
  chekc: 'nm/param/check',
  edit: 'nm/param/maintain',
};
// 金额默认13位整数，2位小数
export const DEFAULT_AMOUNT_PROPS = {
  min: 0,
  max: 9999999999999.99,
  decimal: 2,
  step: 1, // 设置步长为0.1
  precision: 2,
  // stringMode: true,
};
// 请求类型
export const REQUEST_TYPE = {
  mock: 'mock',
  business: 'business',
  param: 'param',
};

// editTable的valueType
export const EDITTABLE_VALUE_TYPE = {
  TEXT: 'text', // 文本
  DIGIT: 'digit', // 数字
  SELECT: 'select', // 选择
};

export const FORM_ERROR_CODE = {
  CHECK_MSG: 'checkMsg',
  EDITING: 'formEditing',
};
// 默认分野参数
export const DEFAULT_PAGINATION = {
  currentPage: 1,
  pageSize: 50,
};
/**
 * 页面国际化常量
 */
export const I18N_COMON_PAGENAME = {
  COMMON: 'common',
  PARAM: 'param',
  SYSTEM: 'system',
};

/**
 * 不属于组件的属性常量
 */
export const NOT_BELONG_COMPONENT = {
  TYPE: 'type',
  VALUE: 'value',
  LABEL: 'label',
  RULES: 'rules',
  DATA: 'data',
  SHOWKEY: 'showKey',
  REF: 'ref',
  BIND: 'bind',
  PREFIX: 'prefix',
};
