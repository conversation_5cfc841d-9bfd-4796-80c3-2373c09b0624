/**
 * 按钮权限常量
 */

const PERMISSION_CONSTANTS = {
  // 角色管理
  ROLE_MANAGE: {
    CREATE: 'system:role:delete',
    EDIT: 'system:role:edit',
    MOUNT_DETAIL: 'system:role:viewPermissionMounting',
    MOUNT_EDIT: 'system:role:editPermissionMounting',
  },
  // 接口配置参数应用服务
  INTERFACE_CONFIG: {
    DETAIL: 'param:gateway:interfaceConfig:detail',
    EDIT: 'param:gateway:interfaceConfig:edit',
    CREATE: 'param:gateway:interfaceConfig:add',
    DELETE: 'param:gateway:interfaceConfig:delete',
  },
  // 接口映射参数应用服务
  INTERFACE_MAP: {
    DETAIL: 'param:gateway:interfaceMap:detail',
    EDIT: 'param:gateway:interfaceMap:edit',
    CREATE: 'param:gateway:interfaceMap:add',
    DELETE: 'param:gateway:interfaceMap:delete1',
    COPY: 'param:gateway:interfaceMap:copy1',
  },
  // 双发白名单服务
  DOUBLE_WHITE_LIST: {
    DETAIL: 'param:gateway:dupluWhite:detail',
    EDIT: 'param:gateway:dupluWhite:edit',
    CREATE: 'param:gateway:dupluWhite:add',
    DELETE: 'param:gateway:dupluWhite:delete',
  },
  // 错误代码映射参数
  ERROR_CODE_MAPPING: {
    DETAIL: 'param:gateway:errorCodeMap:detail',
    EDIT: 'param:gateway:errorCodeMap:edit',
    CREATE: 'param:gateway:errorCodeMap:add',
    DELETE: 'param:gateway:errorCodeMap:delete',
  },
  // 转换配置查找
  CONVER_CONFIG: {
    DETAIL: 'param:gateway:convertConfig:detail',
    EDIT: 'param:gateway:convertConfig:edit',
    CREATE: 'param:gateway:convertConfig:add',
    DELETE: 'param:gateway:convertConfig:delete',
  },
  // 查找配置参数
  CONFIG_PARAM: {
    DETAIL: 'param:gateway:configParam:detail',
    EDIT: 'param:gateway:configParam:edit',
    CREATE: 'param:gateway:configParam:add',
    DELETE: 'param:gateway:configParam:delete',
  },
  // api密钥映射应用服务
  API_KEY_CONFIG: {
    DETAIL: 'param:gateway:apiKeyMap:detail',
    EDIT: 'param:gateway:apiKeyMap:edit',
    CREATE: 'param:gateway:apiKeyMap:add',
    DELETE: 'param:gateway:apiKeyMap:delete',
  },
  // api密钥配置应用服务
  API_KEY_MAPPING: {
    DETAIL: 'param:gateway:apiKeyConfig:detail',
    EDIT: 'param:gateway:apiKeyConfig:edit',
    CREATE: 'param:gateway:apiKeyConfig:add',
    DELETE: 'param:gateway:apiKeyConfig:delete',
  },
};

export default PERMISSION_CONSTANTS;
