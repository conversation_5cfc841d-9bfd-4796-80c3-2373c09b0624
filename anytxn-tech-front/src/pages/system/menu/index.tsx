import useIntlCustom from '@/common/hooks/useIntlCustom';
import { FormAction, FormTemplate, TreeTable } from '@/components';
import Search from '@/components/search';
import LayoutTemplate from '@/components/templates/LayoutTemplate';
import {
  COMPONENT_TYPE,
  NOTIFICATION_TYPE,
  OPERATE_TYPE,
} from '@/constants/publicConstant';
import { getTreeList, updateData } from '@/services/menu';
import { Button, Modal, Row, Tabs, type TabsProps } from 'antd';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import usePageFormConfig from './hooks/usePageFormConfig';
import {
  dataOutlined,
  directionOutlined,
  editorOutlined,
  logoOutlined,
  otherOutlined,
  suggestionOutlined,
} from './iconData';
import { pageConfig } from './pageConfig';
// todo 动态而优雅的导入
import * as iconList from '@ant-design/icons'; // 数据太多，请原谅我这么不优雅的导入

const MenuPage: React.FC = () => {
  const {
    prefix,
    resetValue,
    cardTitle,
    searchSource,
    optionList,
    columns,
    tabTypeEunm,
  } = pageConfig;

  // hook变量
  const formRef = useRef<any>(null);
  const { translate, openNotificationTip } = useIntlCustom();
  const [listData, setListData] = useState<any>([]);
  const [searchData, setSearchData] = useState<any>(resetValue);
  const [type, setType] = useState<any>(OPERATE_TYPE.list);
  const [detailData, setDetailData] = useState<any>({});
  const [tableLoading, setTableLoading] = useState<boolean>(false);
  const [childrenType, setChildrenType] = React.useState<string>(
    COMPONENT_TYPE.TABLE,
  );
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  // 选中icon值
  const [iconValue, setIconValue] = useState<any>(null);
  // 表单数据
  const [formData, setFormData] = useState<any>(null);
  // 上级选单下拉数据
  const [optionData, setOptionData] = useState<any>([]);
  // 未过滤树状选单数据
  const [tableData, setTableData] = useState<any[]>([]);

  // 内部变量
  const iconMap: any = {
    direction: directionOutlined,
    suggestion: suggestionOutlined,
    editor: editorOutlined,
    data: dataOutlined,
    logo: logoOutlined,
    other: otherOutlined,
  };

  const { setEditType, infoFormConfig } = usePageFormConfig(optionData);

  // 副作用
  useEffect(() => {
    getData(searchData);
    handleSearch(resetValue);
  }, []);

  // 逻辑处理
  const getData = async (val: object) => {
    try {
      setTableLoading(true);
      const res: any = await getTreeList(val);
      setTableData(res);
      if (res) {
        setListData(res);
      }
    } catch (e) {
      setListData([]);
      console.log(e);
    } finally {
      setTableLoading(false);
    }
  };

  // 上级选单数据处理
  const traverseTree = (data: any) => {
    data.title = data.menuName;
    data.value = data.menuId;
    const newData = _.omit(data, [
      'menuId',
      'menuName',
      'createTime',
      'createUser',
      'updateTime',
      'updateUser',
      'iconId',
      'type',
      'menuStatus',
      'permissionId',
      'parentMenuId',
      'orderSeq',
      'level',
      'key',
      'menuType',
    ]);
    if (newData.children && newData.children.length > 0) {
      newData.children.forEach((childNode: any) => traverseTree(childNode));
    }
  };

  // 列表过滤数据处理(枚举精准匹配和字符串模糊匹配数据处理)
  const filterTreeData = (data: any, conditions: any, type: string) => {
    const key = Object.keys(conditions)[0];
    // 如果没有任何过滤条件，返回原数据
    if (!conditions[key]) return data;
    // 选单状态和选单名称根据type分别进行精准匹配和模糊过滤
    const result: any = [];
    data.forEach((node: any) => {
      const newNode = { ...node };
      // 如果节点有子节点，则递归搜索子节点
      if (newNode.children) {
        newNode.children = filterTreeData(newNode.children, conditions, type);
        // 检查子节点中是否有匹配的
        const hasMatchingChild = newNode.children.some((child: any) =>
          type === 'eunm'
            ? child[key] === conditions[key] ||
              (child.children && child.children.length > 0)
            : child[key]
                .toLowerCase()
                .includes(conditions[key].toLowerCase()) ||
              (child.children && child.children.length > 0),
        );
        // 如果当前节点本身匹配或者其子节点中有匹配的，则保留该节点
        if (
          type === 'eunm'
            ? newNode[key] === conditions[key] || hasMatchingChild
            : newNode[key]
                .toLowerCase()
                .includes(conditions[key].toLowerCase()) || hasMatchingChild
        ) {
          // 移除空的子节点数组（如果所有子节点都不匹配并被移除）
          if (newNode.children && newNode.children.length === 0) {
            delete newNode.children;
          }
          // 添加节点到结果中
          result.push(newNode);
        }
      } else {
        // 没有子节点的叶子节点：仅当节点本身匹配时才保留
        if (
          type === 'eunm'
            ? newNode[key] === conditions[key]
            : newNode[key].toLowerCase().includes(conditions[key].toLowerCase())
        ) {
          result.push(newNode);
        }
      }
    });
    return result;
  };

  const handleIconChange = (key: any) => {
    setIconValue(key);
  };

  const renderTab = (key: string) => {
    const iconArray = iconMap[key];
    return (
      <>
        {iconArray.map((item: any) => {
          return (
            <Button key={item} onClick={() => handleIconChange(item)} type="text">
              {iconList[item].render()}
            </Button>
          );
        })}
      </>
    );
  };

  const getTabData = () => {
    const res: any = [];
    tabTypeEunm?.map((item: any) => {
      const obj = {
        key: item,
        label: translate(prefix, item),
        children: renderTab(item),
      };
      res.push(obj);
    });
    return res;
  };
  const tabsItems: TabsProps['items'] = getTabData();
  // 事件处理
  const handleSearch = (e: any) => {
    setSearchData(e);
    // 前端编写数据过滤逻辑
    const { menuName, menuStatus } = e;
    let data = JSON.parse(JSON.stringify(tableData));
    const nameCondition = { menuName: menuName };
    const statusCondition = { menuStatus: menuStatus };
    const firstRes = filterTreeData(data, nameCondition, 'string');
    const res = filterTreeData(firstRes, statusCondition, 'eunm');
    // 处理选单状态/选单名称过滤逻辑
    setListData(res);
  };

  const handleCardBack = () => {
    setType(OPERATE_TYPE.list);
    setChildrenType(COMPONENT_TYPE.TABLE);
  };

  // 提交事件
  const handleCardSubmit = async () => {
    try {
      const data = await formRef?.current?.onSubmit();
      const { formData } = data;
      const param = { body: { ...formData, menuId: detailData.menuId } };
      if (data) {
        const res: any = await updateData(param);
        if (res) {
          handleCardBack();
          openNotificationTip(
            'common',
            NOTIFICATION_TYPE.SUCCESS,
            'editSuccess',
            1,
          );
        }
      }
    } catch (e) {
      openNotificationTip('common', NOTIFICATION_TYPE.ERROR, 'checkMsg');
    }
  };

  const handleRadioChange = async (obj: any) => {
    const { e, form } = obj;
    if (e !== undefined && form !== undefined && e.target.name === 'menuType') {
      if (e.target.value === '1') {
        await form.setFieldsValue({ menuRouteUrl: '' });
      }
    }
  };

  // 选择选单icon，弹出icon 弹窗
  const handleIconSearch = (key: string, form: any) => {
    setFormData(form);
    setIsModalOpen(true);
  };

  // 选择icon图标
  const handleModalSubmit = () => {
    formData?.setFieldsValue({ iconId: iconValue });
    setIsModalOpen(false);
  };

  // 列表操作列按钮事件处理
  const handleAction = (type: string, row: any) => {
    // 当执行编辑操作时，处理封装上级选单树形数据
    if (type === OPERATE_TYPE.edit) {
      const data = JSON.parse(JSON.stringify(tableData));
      data.forEach((rootNode: any) => traverseTree(rootNode));
      setOptionData(data);
    }
    setType(type);
    setEditType(type);
    setDetailData(row);
    setChildrenType(COMPONENT_TYPE.FORM);
  };

  // 渲染处理
  const renderSearch = () => {
    return (
      <Search
        searchValue={searchData}
        resetValue={resetValue}
        intlPrefix={prefix}
        searchSource={searchSource}
        onSearch={handleSearch}
      />
    );
  };

  const renderTable = () => {
    return (
      <TreeTable
        rowKey="menuId"
        intlPrefix={prefix}
        dataSource={listData}
        loading={tableLoading}
        optionList={optionList}
        columns={columns}
        onAction={handleAction}
      />
    );
  };

  const renderFormAction = () => {
    return type === OPERATE_TYPE.list || type === OPERATE_TYPE.detail ? (
      <FormAction showSubmit={false} showCreate={false} />
    ) : (
      <FormAction
        showSubmit
        showCreate={false}
        onReset={() => formRef?.current?.onReset()}
        onSubmit={handleCardSubmit}
      />
    );
  };

  const renderForm = () => {
    return (
      <>
        <FormTemplate
          ref={formRef}
          config={infoFormConfig}
          initialData={detailData}
          loading={false}
          intlPrefix={prefix}
          canEdit={type !== OPERATE_TYPE.detail}
          showMaintenance={type === OPERATE_TYPE.detail}
          onChange={handleRadioChange}
          onSearch={handleIconSearch}
        />
        <Modal
          title={translate(prefix, 'iconSelect')}
          cancelText={translate('common', 'cancel')}
          okText={translate('common', 'confirm')}
          width={1000}
          open={isModalOpen}
          onOk={handleModalSubmit}
          onCancel={() => setIsModalOpen(false)}
        >
          <Tabs type="card" items={tabsItems} />
          <Row style={{ paddingTop: '30px' }}>
            {translate(prefix, 'messTip')}
            {iconValue}
          </Row>
        </Modal>
      </>
    );
  };

  return (
    <LayoutTemplate
      searchChildren={renderSearch()}
      tableChildren={renderTable()}
      formChildren={renderForm()}
      childrenType={childrenType}
      type={type}
      intlPrefix={prefix}
      shouldScroll={false}
      cardTitle={cardTitle}
      cardExtra={renderFormAction()}
      cardShowBack={type !== OPERATE_TYPE.list}
      onCardBack={handleCardBack}
    />
  );
};

export default React.memo(MenuPage);
