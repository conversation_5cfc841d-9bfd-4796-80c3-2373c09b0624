import DICT_CONSTANTS from '@/constants/dictConstants';
import {
  COMPONENT_TYPE,
  FORMITEM_TYPE,
  OPERATE_TYPE,
} from '@/constants/publicConstant';
import { useState } from 'react';

const useFormConfig = (optionData: any) => {
  const [type, setEditType] = useState<string>(OPERATE_TYPE.list);

  const filterTreeNode = (inputValue: string, treeNode: any) =>
    treeNode.title.indexOf(inputValue) > -1;

  // 表单字段
  const infoFormConfig = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'menuInfo',
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          name: 'parentMenuId',
          label: 'supMenu',
          type: COMPONENT_TYPE.TREE_SELECT,
          data: optionData,
          showSearch: true,
          allowClear: true,
          placement: 'bottomLeft',
          popupMatchSelectWidth: false,
          disabled: type === OPERATE_TYPE.detail,
          filterTreeNode: filterTreeNode,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'menuName',
          label: 'menuName',
          rules: [{ required: true, max: 64 }],
          maxLength: 64,
          disabled: type === OPERATE_TYPE.detail,
        },
        {
          type: COMPONENT_TYPE.RADIO,
          name: 'menuType',
          label: 'menuType',
          rules: [{ required: true }],
          data: DICT_CONSTANTS.MENU_TYPE,
          showKey: false,
          disabled: type === OPERATE_TYPE.edit,
        },
        {
          type: COMPONENT_TYPE.RADIO,
          name: 'menuStatus',
          label: 'menuStatus',
          rules: [{ required: true }],
          data: DICT_CONSTANTS.MENU_STATUS,
          showKey: false,
          disabled: type === OPERATE_TYPE.detail,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'orderSeq',
          label: 'displaySorting',
          rules: [{ required: true }],
          maxLength: 20,
          disabled: type === OPERATE_TYPE.detail,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'menuRouteUrl',
          label: 'routeUrl',
          // rules: [{ required: !disType, max: 150 }],
          // disabled: disType,
          rules: [{ max: 150 }],
          maxLength: 150,
          disabled: true,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'permissionId',
          label: 'permissionId',
          rules: [{ required: true }],
          maxLength: 64,
          disabled: true,
        },
        {
          type: COMPONENT_TYPE.SEARCH,
          name: 'iconId',
          label: 'iconId',
          readOnly: true,
          disabled: type === OPERATE_TYPE.detail,
          enterButton: 'addIcon',
        },
      ],
    },
  ];

  return {
    setEditType,
    infoFormConfig,
  };
};

export default useFormConfig;
