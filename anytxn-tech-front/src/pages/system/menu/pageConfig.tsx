import DICT_CONSTANTS from '@/constants/dictConstants';
import {
  I18N_COMON_PAGENAME,
  OPERATE_TYPE,
  RENDER_TYPE,
} from '@/constants/publicConstant';
import { IPageConfig } from '@/types/ICommon';

export const pageConfig: IPageConfig = {
  prefix: I18N_COMON_PAGENAME.SYSTEM,
  cardTitle: 'menuInfo',
  resetValue: { menuName: '', menuStatus: 'Y' },
  optionList: [OPERATE_TYPE.detail, OPERATE_TYPE.edit],
  tabTypeEunm: ['direction', 'suggestion', 'editor', 'data', 'logo', 'other'],
  // 搜索条件字段
  searchSource: [
    {
      value: 'menuName',
      label: 'menuName',
      type: 'Input',
    },
    {
      value: 'menuStatus',
      label: 'menuStatus',
      type: 'Select',
      data: DICT_CONSTANTS.MENU_STATUS,
    },
  ],

  // 列表字段
  columns: [
    { key: 'menuName', title: 'menuName', dataIndex: 'menuName', width: 280 },
    { key: 'orderSeq', title: 'sort', dataIndex: 'orderSeq', width: 80 },
    {
      key: 'menuType',
      title: 'menuType',
      dataIndex: 'menuType',
      width: 80,
      prefix: I18N_COMON_PAGENAME.SYSTEM,
      data: DICT_CONSTANTS.MENU_TYPE,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'MENU_TYPE',
    },
    {
      key: 'menuStatus',
      title: 'menuStatus',
      dataIndex: 'menuStatus',
      width: 80,
      prefix: I18N_COMON_PAGENAME.SYSTEM,
      data: DICT_CONSTANTS.MENU_STATUS,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'MENU_STATUS',
    },
    {
      key: 'permissionId',
      title: 'permissionId',
      width: 220,
      dataIndex: 'permissionId',
    },
    { key: 'iconId', title: 'iconId', width: 110, dataIndex: 'iconId' },
    {
      key: 'createTime',
      title: 'createTs',
      dataIndex: 'createTime',
      width: 180,
      align: 'center',
      valueType: RENDER_TYPE.DateTime,
    },
    {
      key: 'updateTime',
      title: 'updateTs',
      dataIndex: 'updateTime',
      width: 180,
      align: 'center',
      valueType: RENDER_TYPE.DateTime,
    },
  ],
};

// 需要查询的参数类型
export const dictEnum = {
  // msgId: DICT_CONSTANTS.DICT_ENUM_MAP.msgId,
};
