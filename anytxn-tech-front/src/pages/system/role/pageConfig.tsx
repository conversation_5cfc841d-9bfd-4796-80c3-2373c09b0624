import DICT_CONSTANTS from '@/constants/dictConstants';
import PERMISSION_CONSTANTS from '@/constants/permissionConstants';
import {
  COMPONENT_TYPE,
  I18N_COMON_PAGENAME,
  RENDER_TYPE,
} from '@/constants/publicConstant';

// 角色状态枚举
export const roleStatusEunm = [
  { key: 'Y', label: 'enable' },
  { key: 'N', label: 'unEnable' },
];

// 挂载状态枚举
export const mountstatusEunm = [
  { key: 'Y', label: 'mounted' },
  { key: 'N', label: 'unmounted' },
];

export const pageConfig = {
  // 国际化前缀
  prefix: I18N_COMON_PAGENAME.SYSTEM,
  resetValue: { roleName: '', roleStatus: null, mountStatus: null },
  // 查询条件字段
  searchSource: [
    {
      value: 'roleName',
      label: 'roleName',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'roleStatus',
      label: 'roleStatus',
      type: COMPONENT_TYPE.SELECT,
      data: roleStatusEunm.map((item) => ({
        key: item.key,
        value: item.label,
      })),
    },
    {
      value: 'mountStatus',
      label: 'mountStatus',
      type: COMPONENT_TYPE.SELECT,
      data: mountstatusEunm.map((item) => ({
        key: item.key,
        value: item.label,
      })),
    },
  ],
  // 页面标题
  cardTitle: 'role',
  // 列表字段
  columns: [
    {
      title: 'roleId',
      dataIndex: 'roleId',
      key: 'roleId',
      width: 60,
    },
    {
      title: 'roleName',
      dataIndex: 'roleName',
      key: 'roleName',
      width: 200,
    },
    {
      key: 'roleStatus',
      dataIndex: 'roleStatus',
      title: 'roleStatus',
      width: 80,
      valueType: RENDER_TYPE.MockDictionary,
      data: DICT_CONSTANTS.ROLE_STATUS_EUNM,
    },
    {
      key: 'mountStatus',
      dataIndex: 'mountStatus',
      title: 'mountStatus',
      width: 100,
      valueType: RENDER_TYPE.MockDictionary,
      data: DICT_CONSTANTS.MOUNT_STATUS_EUNM,
    },
  ],
  // 新增按钮权限
  formActionPermissionObj: {
    create: PERMISSION_CONSTANTS.ROLE_MANAGE.CREATE,
  },
};
