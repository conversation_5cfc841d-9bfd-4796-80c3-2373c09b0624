import useIntlCustom from '@/common/hooks/useIntlCustom';
import { useModel } from '@umijs/max';
import { Button } from 'antd';
import React from 'react';

const Welcome: React.FC = () => {
  const { userInfo, setUserData, userData } = useModel('demo' as any) as any;
  const { translate } = useIntlCustom();

  const handleGetUserInfo = () => {
    userInfo.run({ org: '101', user: 'admin' }).then((res: any) => {
      setUserData(res);
    });
  };

  return (
    <div>
      <Button loading={userInfo.loading} onClick={handleGetUserInfo}>
        {translate('common', 'bankName', { name: userData?.gender })}
      </Button>
      {userData ? <p style={{margin: 12}}>我是Mock接口数据噢：{JSON.stringify(userData)}</p> : null}
    </div>
  );
};

export default Welcome;
