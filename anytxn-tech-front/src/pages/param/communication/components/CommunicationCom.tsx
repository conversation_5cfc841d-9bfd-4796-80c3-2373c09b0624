import useIntlCustom from '@/common/hooks/useIntlCustom';
import { FormTemplate } from '@/components/form';
import GradientButton from '@/components/gradientButton';
import PageFormTemplate from '@/components/templates/PageFormTemplate';
import {
  I18N_COMON_PAGENAME,
  NOTIFICATION_TYPE,
} from '@/constants/publicConstant';
import commonServices from '@/services/common';
import { IFormConfig } from '@/types/IForm';
import React, { ReactNode, useRef } from 'react';
interface CommunicationType {
  title: string;
  url: string;
  initialData?: object;
  formConfig: IFormConfig[];
  onChange?: (data: any, allValue: any) => void;
}

const prefix = I18N_COMON_PAGENAME.PARAM;
const Communication: React.FC<CommunicationType> = ({
  title,
  formConfig,
  url,
  initialData,
  onChange,
}) => {
  // hooks
  const infoRef = useRef<any>(null);
  const { translate, openNotificationTip } = useIntlCustom();

  // 提交
  const handleSubmit = async (): Promise<void> => {
    const formData = await infoRef.current?.onSubmit();
    if (formData) {
      const res = await commonServices.getEditPostBiz({
        interFaceurl: url,
        ...formData?.formData,
      });
      if (res) {
        return openNotificationTip(
          I18N_COMON_PAGENAME.COMMON,
          NOTIFICATION_TYPE.SUCCESS,
          'operationSuccess',
          1,
        );
      }
    }
  };

  // 卡片右侧自定义按钮
  const extraNode = (): ReactNode => {
    return (
      <GradientButton type="primary" size="middle" onClick={handleSubmit}>
        {translate(I18N_COMON_PAGENAME.COMMON, 'submit')}
      </GradientButton>
    );
  };

  return (
    <PageFormTemplate title={title} prefix={prefix} extra={extraNode()}>
      <FormTemplate
        ref={infoRef}
        config={formConfig}
        initialData={initialData}
        canEdit
        intlPrefix={prefix}
        showMaintenance={false}
        onChange={onChange}
      />
    </PageFormTemplate>
  );
};

export default Communication;
