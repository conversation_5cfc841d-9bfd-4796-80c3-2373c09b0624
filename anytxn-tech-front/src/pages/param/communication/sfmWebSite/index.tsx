// 系统管理/通讯管理/SFM網路管理
import { lazy, FC, memo } from 'react';
import { COMPONENT_TYPE, FORMITEM_TYPE } from '@/constants/publicConstant';
import DICT_CONSTANTS from '@/constants/dictConstants';
import urlConstants from '@/constants/urlConstants';

const CommunicationCom = lazy(() => import('../components/CommunicationCom'));

const SFMWebSite: FC = () => {
  // 表单配置项
  const formConfig = [
    {
      type: FORMITEM_TYPE.Single,
      data: [
        {
          width: 300,
          type: COMPONENT_TYPE.INPUT,
          name: 'cardGroup',
          label: 'cardGroup',
          rules: [{ required: true }],
          disabled: true,
        },
        {
          width: 300,
          type: COMPONENT_TYPE.SELECT,
          name: 'func',
          label: 'func',
          rules: [{ required: true }],
          data: DICT_CONSTANTS.SFM_GONGG_NENEG,
        },
      ],
    },
  ];
  // 初始化数据
  const initialData = {
    cardGroup: 'SFM',
  };

  return (
    <CommunicationCom
      title="SFM"
      formConfig={formConfig}
      url={urlConstants.EXPORT_SIGN.EDIT}
      initialData={initialData}
    />
  );
};

export default memo(SFMWebSite);
