// 系统管理/通讯管理/发卡侧
import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, FORMITEM_TYPE } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import React, { useState } from 'react';

const CommunicationCom = React.lazy(
  () => import('../components/CommunicationCom'),
);

const CardIssuingNetManage: React.FC = () => {
  const formInitData = [
    {
      width: 300,
      type: COMPONENT_TYPE.SELECT,
      name: 'cardGroup',
      label: 'cardGroup',
      rules: [{ required: true }],
      data: DICT_CONSTANTS.WSD_PASSAGE_WAY,
    },
    {
      width: 300,
      type: COMPONENT_TYPE.SELECT,
      name: 'func',
      label: 'func',
      rules: [{ required: true }],
      data: DICT_CONSTANTS.WSD_GONGG_NENEG,
    },
    {
      width: 300,
      type: COMPONENT_TYPE.INPUT,
      name: 'cardBin',
      label: 'cardBin',
      rules: [{ required: true }],
    },
  ];

  const [formConfigData, setFormConfigData] = useState(
    formInitData.filter((item) => item.name !== 'cardBin'),
  );

  // 表单配置项
  const formConfig = [
    {
      type: FORMITEM_TYPE.Single,
      data: formConfigData,
    },
  ];

  // 动态修改表单显示内容
  const handleOnChange = (data: any, allValue: any) => {
    if (allValue.cardGroup === 'MC' && ['4', '5'].includes(allValue?.func)) {
      setFormConfigData(formInitData);
    } else {
      setFormConfigData(formInitData.filter((item) => item.name !== 'cardBin'));
    }
  };

  return (
    <CommunicationCom
      title="cardIssuingNetManage"
      formConfig={formConfig}
      url={urlConstants.TRANSACTION_SIGN.EDIT}
      onChange={handleOnChange}
    />
  );
};

export default React.memo(CardIssuingNetManage);
