// 系统管理/通讯管理/管道通訊狀態查詢
import { FC, memo, ReactNode, useState } from 'react';
import { IColumns } from '@/types/ICommon';
import useIntlCustom from '@/common/hooks/useIntlCustom';
import { CommonTable, GradientButton } from '@/components';
import PageFormTemplate from '@/components/templates/PageFormTemplate';
import {
  I18N_COMON_PAGENAME,
  NOTIFICATION_TYPE,
  RENDER_TYPE,
} from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import commonServices from '@/services/common';
import { IChannelComStsData } from './IChannelComStsQuery';
import DICT_CONSTANTS from '@/constants/dictConstants';

// 国际化前缀
const prefix = I18N_COMON_PAGENAME.PARAM;

const ChannelComStsQuery: FC = () => {
  const { translate, openNotificationTip } = useIntlCustom();
  const [dataSource, setDataSource] = useState<IChannelComStsData[]>([]);

  // 列表展示字段
  const columns: IColumns[] = [
    {
      title: 'chanel',
      dataIndex: 'chanl',
      key: 'chanl',
      width: 80,
    },
    {
      key: 'chanlState',
      title: 'chanlState',
      dataIndex: 'chanlState',
      width: 120,
      data: DICT_CONSTANTS.WSD_GONGG_NENEG,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'WSD_GONGG_NENEG',
    },
    {
      key: 'lastOperateTime',
      title: 'lastOperateTime',
      dataIndex: 'lastOperateTime',
      width: 120,
      valueType: RENDER_TYPE.DateTime,
    },
  ];
  // 查询
  const handleSearch = async (): Promise<void> => {
    const res = await commonServices.getTableListDataBiz({
      interFaceurl: urlConstants.EXPORT_CHANEL_SIGN.LIST,
    });
    // if (res?.data) {
    //   setDataSource(res.data?.chanelStateList);
    //   openNotificationTip(
    //     I18N_COMON_PAGENAME.COMMON,
    //     NOTIFICATION_TYPE.SUCCESS,
    //     'operationSuccess',
    //     1,
    //   );
    // }
  };

  // 卡片右侧自定义按钮
  const extraNode = (): ReactNode => {
    return (
      <GradientButton type="primary" size="middle" onClick={handleSearch}>
        {translate(I18N_COMON_PAGENAME.COMMON, 'search')}
      </GradientButton>
    );
  };

  return (
    <PageFormTemplate
      title="chanelComStatus"
      prefix={prefix}
      extra={extraNode()}
    >
      <CommonTable
        rowKey="id"
        serviceType="business"
        dataSource={dataSource}
        columns={columns}
        paginationConfig={false}
        intlPrefix={prefix}
      />
    </PageFormTemplate>
  );
};

export default memo(ChannelComStsQuery);
