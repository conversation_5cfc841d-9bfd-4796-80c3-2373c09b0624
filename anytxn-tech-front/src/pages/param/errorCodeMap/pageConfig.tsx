import PERMISSION_CONSTANTS from '@/constants/permissionConstants';
import {
  COMPONENT_TYPE,
  I18N_COMON_PAGENAME,
  OPERATE_TYPE,
  RENDER_TYPE,
} from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { IPageConfig } from '@/types/ICommon';

const getRequestData = (postData: any, type: string) => {
  let data = {};
  switch (type) {
    case OPERATE_TYPE.create:
    case OPERATE_TYPE.edit:
      data = { ...postData.formData, type };
      break;
    case OPERATE_TYPE.delete:
      data = { type, incId: postData.incId };
      break;
    default:
      return { ...postData };
  }
  return { ...data };
};

export const pageConfig: IPageConfig = {
  prefix: I18N_COMON_PAGENAME.PARAM,
  cardTitle: 'errorCode',
  // 页面接口请求
  urlObj: {
    list: urlConstants.ERROR_CODE_MAP.LIST,
    create: urlConstants.ERROR_CODE_MAP.CREATE,
    edit: urlConstants.ERROR_CODE_MAP.EDIT,
    delete: urlConstants.ERROR_CODE_MAP.DELETE,
    getRequestData,
    
  },
  resetValue: { newErrCode: '', errCode: '', errMsg: '' },
  // 搜索条件字段
  searchSource: [
    {
      value: 'newErrCode',
      label: 'newErrCode',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'errCode',
      label: 'errCode',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'errMsg',
      label: 'errMsg',
      type: COMPONENT_TYPE.INPUT,
    },
  ],

  // 列表字段
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      width: 80,
    },
    {
      title: 'newErrCode',
      dataIndex: 'newErrCode',
      key: 'newErrCode',
      width: 120,
    },
    {
      title: 'errCode',
      dataIndex: 'errCode',
      key: 'errCode',
      width: 150,
    },
    {
      key: 'errMsg',
      title: 'errMsg',
      dataIndex: 'errMsg',
    },
    {
      title: 'createTime',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
      valueType: RENDER_TYPE.DateTime,
    },
    {
      width: 150,
      title: 'updateTime',
      dataIndex: 'updateTime',
      key: 'updateTime',
      valueType: RENDER_TYPE.DateTime,
    },
  ],

  // 表单字段
  infoFormConfig: [
    {
      type: 'FormHearder',
      title: 'baseInfo',
    },
    {
      type: 'Row',
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'newErrCode',
          label: 'newErrCode',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'errCode',
          label: 'errCode',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.TEXTAREA,
          name: 'errMsg',
          label: 'errMsg',
          rules: [{ required: true }],
        },
      ],
    },
  ],
  // 操作列
  optionList: [
    { type: OPERATE_TYPE.detail, permissionId: PERMISSION_CONSTANTS.ERROR_CODE_MAPPING.DETAIL },
    { type: OPERATE_TYPE.edit, permissionId: PERMISSION_CONSTANTS.ERROR_CODE_MAPPING.EDIT },
    { type: OPERATE_TYPE.delete, permissionId: PERMISSION_CONSTANTS.ERROR_CODE_MAPPING.DELETE },
  ],
  // 新增按钮权限
  formActionPermissionObj: {
    create: PERMISSION_CONSTANTS.ERROR_CODE_MAPPING.CREATE,
  },
};

// 需要查询的参数类型
export const dictEnum = {
  // msgId: DICT_CONSTANTS.DICT_ENUM_MAP.msgId,
};
