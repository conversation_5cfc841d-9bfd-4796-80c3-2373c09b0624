import DICT_CONSTANTS from '@/constants/dictConstants';
import PERMISSION_CONSTANTS from '@/constants/permissionConstants';
import {
  COMPONENT_TYPE,
  DATE_FORMATE_SECONG,
  I18N_COMON_PAGENAME,
  OPERATE_TYPE,
  RENDER_TYPE,
  TIME_FORMATE,
} from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { IPageConfig } from '@/types/ICommon';
import dayjs from 'dayjs';

const getRequestData = (postData: any, type: string) => {
  let data = {};
  switch (type) {
    case OPERATE_TYPE.create:
    case OPERATE_TYPE.edit:
      data = {
        type,
        ...postData.formData,
        activationTime: dayjs(postData.formData?.activationTime).format(
          DATE_FORMATE_SECONG,
        ),
        expirationTime: dayjs(postData.formData?.expirationTime).format(
          DATE_FORMATE_SECONG,
        ),
      };
      break;
    case OPERATE_TYPE.delete:
      data = { type, incId: postData.incId };
      break;
    default:
      return { ...postData };
  }
  return { ...data };
};

export const pageConfig: IPageConfig = {
  prefix: I18N_COMON_PAGENAME.PARAM,
  cardTitle: 'apiKeyConfig',
  // 页面接口请求
  urlObj: {
    list: urlConstants.API_KEY_CONFIG.LIST,
    create: urlConstants.API_KEY_CONFIG.CREATE,
    edit: urlConstants.API_KEY_CONFIG.EDIT,
    delete: urlConstants.API_KEY_CONFIG.DELETE,
    getRequestData,
    
  },
  resetValue: { apiKey: '', sourceChannel: '', status: null },
  // 搜索条件字段
  searchSource: [
    {
      value: 'apiKey',
      label: 'apiKey',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'sourceChannel',
      label: 'apiSourceChannel',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'status',
      label: 'status',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.PARAM_STATUS,
    },
  ],

  // 列表字段
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      width: 80,
    },
    {
      title: 'apiKey',
      dataIndex: 'apiKey',
      key: 'apiKey',
      width: 150,
    },
    {
      title: 'apiSourceChannel',
      dataIndex: 'sourceChannel',
      key: 'sourceChannel',
      width: 150,
    },
    {
      title: 'activationTime',
      dataIndex: 'activationTime',
      key: 'activationTime',
      width: 150,
      valueType: RENDER_TYPE.DateTime,
    },
    {
      title: 'expirationTime',
      dataIndex: 'expirationTime',
      key: 'expirationTime',
      width: 150,
      valueType: RENDER_TYPE.DateTime,
    },
    {
      width: 100,
      key: 'status',
      title: 'status',
      dataIndex: 'status',
      data: DICT_CONSTANTS.PARAM_STATUS,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'PARAM_STATUS',
    },
  ],

  // 表单字段
  infoFormConfig: [
    {
      type: 'FormHearder',
      title: 'baseInfo',
    },
    {
      type: 'Row',
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'apiKey',
          label: 'apiKey',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'sourceChannel',
          label: 'apiSourceChannel',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.DATE_PICKER,
          name: 'activationTime',
          label: 'activationTime',
          showTime: true,
          format: TIME_FORMATE,
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.DATE_PICKER,
          name: 'expirationTime',
          label: 'expirationTime',
          showTime: true,
          format: TIME_FORMATE,
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'status',
          label: 'status',
          rules: [{ required: true }],
          data: DICT_CONSTANTS.PARAM_STATUS,
          // disabled: true,
        },
      ],
    },
  ],
  // 操作列
  optionList: [
    { type: OPERATE_TYPE.detail, permissionId: PERMISSION_CONSTANTS.API_KEY_CONFIG.DETAIL },
    { type: OPERATE_TYPE.edit, permissionId: PERMISSION_CONSTANTS.API_KEY_CONFIG.EDIT },
    { type: OPERATE_TYPE.delete, permissionId: PERMISSION_CONSTANTS.API_KEY_CONFIG.DELETE },
  ],
  // 新增按钮权限
  formActionPermissionObj: {
    create: PERMISSION_CONSTANTS.API_KEY_CONFIG.CREATE,
  },
};

// 需要查询的参数类型
export const dictEnum = {
  // msgId: DICT_CONSTANTS.DICT_ENUM_MAP.msgId,
};
