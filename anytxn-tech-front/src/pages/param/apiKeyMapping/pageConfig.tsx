import PERMISSION_CONSTANTS from '@/constants/permissionConstants';
import {
  COMPONENT_TYPE,
  I18N_COMON_PAGENAME,
  OPERATE_TYPE,
} from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { IPageConfig } from '@/types/ICommon';

const getRequestData = (postData: any, type: string) => {
  let data = {};
  switch (type) {
    case OPERATE_TYPE.create:
    case OPERATE_TYPE.edit:
      data = { ...postData.formData, type };
      break;
    case OPERATE_TYPE.delete:
      data = { type, incId: postData.incId };
      break;
    default:
      return { ...postData };
  }
  return { ...data };
};

export const pageConfig: IPageConfig = {
  prefix: I18N_COMON_PAGENAME.PARAM,
  cardTitle: 'apiKeyMapping',
  // 页面接口请求
  urlObj: {
    list: urlConstants.API_KEY_MAPPING.LIST,
    create: urlConstants.API_KEY_MAPPING.CREATE,
    edit: urlConstants.API_KEY_MAPPING.EDIT,
    delete: urlConstants.API_KEY_MAPPING.DELETE,
    getRequestData,
  },

  resetValue: { apiKey: '', msgId: '' },
  // 搜索条件字段
  searchSource: [
    {
      value: 'apiKey',
      label: 'apiKey',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'msgId',
      label: 'apiMsgId',
      type: COMPONENT_TYPE.INPUT,
    },
  ],

  // 列表字段
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      width: 80,
    },
    {
      title: 'apiKey',
      dataIndex: 'apiKey',
      key: 'apiKey',
      width: 120,
    },
    {
      title: 'apiMsgId',
      dataIndex: 'msgId',
      key: 'msgId',
      width: 150,
    },
  ],

  // 表单字段
  infoFormConfig: [
    {
      type: 'FormHearder',
      title: 'baseInfo',
    },
    {
      type: 'Row',
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'apiKey',
          label: 'apiKey',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'msgId',
          label: 'apiMsgId',
          rules: [{ required: true }],
        },
      ],
    },
  ],
  // 操作列
  optionList: [
    { type: OPERATE_TYPE.detail, permissionId: PERMISSION_CONSTANTS.API_KEY_MAPPING.DETAIL },
    { type: OPERATE_TYPE.edit, permissionId: PERMISSION_CONSTANTS.API_KEY_MAPPING.EDIT },
    { type: OPERATE_TYPE.delete, permissionId: PERMISSION_CONSTANTS.API_KEY_MAPPING.DELETE },
  ],
  // 新增按钮权限
  formActionPermissionObj: {
    create: PERMISSION_CONSTANTS.API_KEY_MAPPING.CREATE,
  },
};

// 需要查询的参数类型
export const dictEnum = {
  // msgId: DICT_CONSTANTS.DICT_ENUM_MAP.msgId,
};
