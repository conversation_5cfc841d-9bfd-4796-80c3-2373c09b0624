// 参数管理/接口映射参数应用服务
import { OPERATE_TYPE } from '@/constants/publicConstant';
import React, { Suspense, useState } from 'react';
import { IDoubleWhiteRow } from './IDoubleWhite';
import { dictEnum, pageConfig } from './pageConfig';

const PageTemplate = React.lazy(
  () => import('@/components/templates/PageTemplate'),
);

const DoubleWhitePage: React.FC = () => {
  const {
    prefix,
    urlObj,
    cardTitle,
    resetValue,
    searchSource,
    columns,
    optionList,
    formActionPermissionObj,
    infoFormConfig,
  } = pageConfig;
  const [detailData, setDetailData] = useState<IDoubleWhiteRow>({});

  // 查询条件配置
  const searchConfig = {
    searchSource: searchSource,
    searchValue: { ...resetValue },
    resetValue,
  };

  // 操作栏配置
  const formActionConfig = { permissionObj: formActionPermissionObj };

  // 表格配置
  const tableConfig = {
    columns,
    optionList,
    rowKey: 'paramIndex',
    showPagination: true,
  };

  // 表单配置
  const formConfig = {
    config: infoFormConfig,
    data: detailData,
    intlPrefix: prefix,
    onChange: () => {},
  };

  // 列表按钮操作
  const handleAction = (editType: string, row: IDoubleWhiteRow) => {
    setDetailData({ ...row });
  };

  return (
    <Suspense>
      <PageTemplate
        searchConfig={searchConfig}
        tableConfig={tableConfig}
        formConfig={formConfig}
        urlObj={urlObj}
        formActionConfig={formActionConfig}
        cardTitle={cardTitle}
        intlPrefix={prefix}
        dictEnum={dictEnum}
        onAction={handleAction}
      />
    </Suspense>
  );
};
export default React.memo(DoubleWhitePage);
