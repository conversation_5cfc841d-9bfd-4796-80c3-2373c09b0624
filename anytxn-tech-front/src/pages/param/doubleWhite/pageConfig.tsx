import DICT_CONSTANTS from '@/constants/dictConstants';
import PERMISSION_CONSTANTS from '@/constants/permissionConstants';
import {
  COMPONENT_TYPE,
  I18N_COMON_PAGENAME,
  OPERATE_TYPE,
  RENDER_TYPE,
} from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { IPageConfig } from '@/types/ICommon';

const getRequestData = (postData: any, type: string) => {
  let data = {};
  switch (type) {
    case OPERATE_TYPE.create:
    case OPERATE_TYPE.edit:
      data = { ...postData.formData, type };
      break;
    case OPERATE_TYPE.delete:
      data = { type, incId: postData.incId };
      break;
    default:
      return { ...postData };
  }
  return { ...data };
};

export const pageConfig: IPageConfig = {
  prefix: I18N_COMON_PAGENAME.PARAM,
  cardTitle: 'doubleWhite',
  // 页面接口请求
  urlObj: {
    list: urlConstants.DOUBLE_WHITE_LIST.LIST,
    create: urlConstants.DOUBLE_WHITE_LIST.CREATE,
    edit: urlConstants.DOUBLE_WHITE_LIST.EDIT,
    delete: urlConstants.DOUBLE_WHITE_LIST.DELETE,
    getRequestData,
    
  },

  resetValue: { custType: '', custRecogNo: '', status: null },
  // 搜索条件字段
  searchSource: [
    {
      value: 'custType',
      label: 'custType',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'custRecogNo',
      label: 'custRecogNo',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'status',
      label: 'status',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.PARAM_STATUS,
    },
  ],

  // 列表字段
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      width: 80,
    },
    {
      title: 'custType',
      dataIndex: 'custType',
      key: 'custType',
      width: 120,
    },
    {
      title: 'custRecogNo',
      dataIndex: 'custRecogNo',
      key: 'custRecogNo',
      width: 150,
    },
    {
      width: 100,
      key: 'status',
      title: 'status',
      dataIndex: 'status',
      data: DICT_CONSTANTS.PARAM_STATUS,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'PARAM_STATUS',
    },
    {
      width: 180,
      title: 'createTime',
      dataIndex: 'createTime',
      key: 'createTime',
      valueType: RENDER_TYPE.DateTime,
    },
    {
      width: 180,
      title: 'updateTime',
      dataIndex: 'updateTime',
      key: 'updateTime',
      valueType: RENDER_TYPE.DateTime,
    },
  ],

  // 表单字段
  infoFormConfig: [
    {
      type: 'FormHearder',
      title: 'baseInfo',
    },
    {
      type: 'Row',
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'custType',
          label: 'custType',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'custRecogNo',
          label: 'custRecogNo',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'status',
          label: 'status',
          rules: [{ required: true }],
          data: DICT_CONSTANTS.PARAM_STATUS,
          // disabled: true,
        },
      ],
    },
  ],
  // 操作列
  optionList: [
    { type: OPERATE_TYPE.detail, permissionId: PERMISSION_CONSTANTS.DOUBLE_WHITE_LIST.DETAIL },
    { type: OPERATE_TYPE.edit, permissionId: PERMISSION_CONSTANTS.DOUBLE_WHITE_LIST.EDIT },
    { type: OPERATE_TYPE.delete, permissionId: PERMISSION_CONSTANTS.DOUBLE_WHITE_LIST.DELETE },
  ],
  // 新增按钮权限
  formActionPermissionObj: {
    create: PERMISSION_CONSTANTS.DOUBLE_WHITE_LIST.CREATE,
  },
};

// 需要查询的参数类型
export const dictEnum = {
  // msgId: DICT_CONSTANTS.DICT_ENUM_MAP.msgId,
};
