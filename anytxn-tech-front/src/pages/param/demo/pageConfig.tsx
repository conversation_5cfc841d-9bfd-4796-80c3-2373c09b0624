import DICT_CONSTANTS from '@/constants/dictConstants';
import { OPERATE_TYPE, COMPONENT_TYPE, RENDER_TYPE } from '@/constants/publicConstant';

const getRequestPostData = (postData: any) => {
  const params = {
    pageId: 'auth_resp_code_param',
    bizKey: ['rejRsnCode'],
    data: { ...postData },
  };
  return params;
};
const getRequestData = (postData: any, type: string) => {
  let data = {};
  switch (type) {
    case OPERATE_TYPE.create:
    case OPERATE_TYPE.edit:
      data = { ...postData.formData, type };
      break;
    case OPERATE_TYPE.delete:
      data = { type, incId: postData.incId };
      break;
    default:
      return { ...postData };
  }
  return getRequestPostData(data);
};
export const pageConfig = {
  prefix: 'demo',
  pageId: 'auth_resp_code_param',
  cardTitle: 'authorizationResCode',

  // 页面接口请求
  urlObj: {
    list: '',
    create: '',
    delete: '',
    detail: '',
    edit: '',
    getRequestData,
  },
  // 搜索
  searchSource: [
    {
      value: 'rejRsnCode',
      label: 'rejRsnCode',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'sysInnerRespCode',
      label: 'sysInnerRespCode',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'paramSts',
      label: 'paramSts',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.PARAM_STATUS,
    },
  ],

  // 列表
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      width: 80,
    },
    {
      title: 'crcdOrgNo',
      dataIndex: 'crcdOrgNo',
      key: 'crcdOrgNo',
      width: 120,
    },
    {
      title: 'rejRsnCode',
      dataIndex: 'rejRsnCode',
      key: 'rejRsnCode',
      width: 120,
    },
    {
      title: 'rejRsnCodePri',
      dataIndex: 'rejRsnCodePri',
      key: 'rejRsnCodePri',
      width: 150,
    },
    {
      title: 'sysInnerRespCode',
      dataIndex: 'sysInnerRespCode',
      key: 'sysInnerRespCode',
      width: 150,
    },
    {
      width: 150,
      title: 'rejRsnCodeDesc',
      dataIndex: 'rejRsnCodeDesc',
      key: 'rejRsnCodeDesc',
      valueType: RENDER_TYPE.Ellipsis,
    },
    {
      title: 'mcsRespCode',
      dataIndex: 'mcsRespCode',
      key: 'mcsRespCode',
      width: 120,
    },
    {
      key: 'mcRespCode',
      title: 'mcRespCode',
      dataIndex: 'mcRespCode',
      width: 120,
    },
    {
      key: 'visaRespCode',
      title: 'visaRespCode',
      dataIndex: 'visaRespCode',
      width: 120,
    },
    {
      key: 'ecsRespCode',
      title: 'ecsRespCode',
      dataIndex: 'ecsRespCode',
      width: 120,
    },
    {
      width: 100,
      key: 'paramSts',
      title: 'paramSts',
      dataIndex: 'paramSts',
      data: DICT_CONSTANTS.PARAM_STATUS,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'PARAM_STATUS',
    },
    {
      key: 'updateTime',
      title: 'updateTime',
      dataIndex: 'updateTime',
      width: 160,
    },
    {
      key: 'updateUser',
      title: 'updateUser',
      dataIndex: 'updateUser',
      width: 120,
    },
  ],

  // 表单
  infoFormConfig: [
    {
      type: 'FormHearder',
      title: 'baseInfo',
    },
    {
      type: 'Row',
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'crcdOrgNo',
          label: 'crcdOrgNo',
          rules: [{ required: true }],
          // disabled: true,
          maxLength: 4,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'rejRsnCode',
          label: 'rejRsnCode',
          maxLength: 14,
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'rejRsnCodePri',
          label: 'rejRsnCodePri',
          maxLength: 4,
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'sysInnerRespCode',
          label: 'sysInnerRespCode',
          maxLength: 2,
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'mcsRespCode',
          label: 'mcsRespCode',
          maxLength: 2,
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'mcRespCode',
          label: 'mcRespCode',
          maxLength: 2,
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'visaRespCode',
          label: 'visaRespCode',
          maxLength: 2,
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'ecsRespCode',
          label: 'ecsRespCode',
          maxLength: 2,
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'paramSts',
          label: 'paramSts',
          rules: [{ required: true }],
          data: DICT_CONSTANTS.PARAM_STATUS,
        },
        {
          type: COMPONENT_TYPE.TEXTAREA,
          name: 'rejRsnCodeDesc',
          label: 'rejRsnCodeDesc',
          maxLength: 256,
          rules: [{ required: true }],
        },
      ],
    },
  ],
};
