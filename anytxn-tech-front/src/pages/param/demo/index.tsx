import { updateDisabledProperty } from '@/utils/comUtil';
import React, { Suspense, useState } from 'react';
import { pageConfig } from './pageConfig';
const PageTemplate = React.lazy(
  () => import('@/components/templates/PageTemplate'),
);

const authorizationResPage: React.FC = () => {
  const resetValue = { rejRsnCode: '', sysInnerRespCode: '', paramSts: null };
  const [detailData, setDetailData] = useState<any>({});

  /**
   *
   * @param editType 操作事件类型
   * @param row 该条数据
   */
  const handleAction = (editType, row) => {
    setDetailData({ ...row });
  };

  const searchConfig = {
    searchSource: pageConfig.searchSource,
    searchValue: { ...resetValue },
    resetValue,
    defaultParam: { pageId: pageConfig.pageId },
  };

  const tableConfig = {
    columns: pageConfig.columns,
    rowKey: 'paramIndex',
    showPagination: true,
    props: { scroll: { x: 1500, y: 2000 } },
  };

  const formConfig = {
    config: pageConfig.infoFormConfig,
    data: detailData,
    intlPrefix: pageConfig.prefix,
    onChange: () => {},
  };
  return (
    <Suspense>
      <PageTemplate
        searchConfig={searchConfig}
        tableConfig={tableConfig}
        formConfig={formConfig}
        urlObj={pageConfig.urlObj}
        cardTitle={pageConfig.cardTitle}
        intlPrefix={pageConfig.prefix}
        onAction={handleAction}
      />
    </Suspense>
  );
};
export default React.memo(authorizationResPage);
