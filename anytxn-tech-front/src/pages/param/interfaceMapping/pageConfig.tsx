import DICT_CONSTANTS from '@/constants/dictConstants';
import PERMISSION_CONSTANTS from '@/constants/permissionConstants';
import {
  COMPONENT_TYPE,
  I18N_COMON_PAGENAME,
  OPERATE_TYPE,
  RENDER_TYPE,
} from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { IPageConfig } from '@/types/ICommon';

const getRequestData = (postData: any, type: string) => {
  let data = {};
  switch (type) {
    case OPERATE_TYPE.create:
    case OPERATE_TYPE.edit:
      data = { ...postData.formData, type };
      break;
    case OPERATE_TYPE.delete:
      data = { type, incId: postData.incId };
      break;
    default:
      return { ...postData };
  }
  return { ...data };
};

export const pageConfig: IPageConfig = {
  prefix: I18N_COMON_PAGENAME.PARAM,
  cardTitle: 'interfaceMapping',
  // 页面接口请求
  urlObj: {
    list: urlConstants.INTERFACE_MAPPING.LIST,
    copy: urlConstants.INTERFACE_MAPPING.CREATE,
    create: urlConstants.INTERFACE_MAPPING.CREATE,
    edit: urlConstants.INTERFACE_MAPPING.EDIT,
    delete: urlConstants.INTERFACE_MAPPING.DELETE,
    getRequestData,
  },
  resetValue: {
    msgId: '',
    sourceChannel: '',
    linkMode: null,
    url: '',
    protocol: '',
    dualSwitch: null,
    recordSwitch: null,
  },

  // 搜索条件字段
  searchSource: [
    {
      value: 'msgId',
      label: 'msgId',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'sourceChannel',
      label: 'sourceChannel',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'linkMode',
      label: 'linkMode',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.LIKE_MODE,
    },
    {
      value: 'url',
      label: 'url',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'protocol',
      label: 'protocol',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'dualSwitch',
      label: 'dualSwitch',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.COMMON_SWITCH,
    },
    {
      value: 'recordSwitch',
      label: 'recordSwitch',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.COMMON_SWITCH,
    },
  ],

  // 列表字段
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      width: 60,
    },
    {
      title: 'msgId',
      dataIndex: 'msgId',
      key: 'msgId',
      width: 120,
    },
    {
      title: 'sourceChannel',
      dataIndex: 'sourceChannel',
      key: 'sourceChannel',
      width: 120,
    },
    {
      title: 'url',
      dataIndex: 'url',
      key: 'url',
      width: 150,
    },
    {
      title: 'module',
      dataIndex: 'module',
      key: 'module',
    },
    {
      title: 'protocol',
      dataIndex: 'protocol',
      key: 'protocol',
      width: 60,
    },
    {
      title: 'linkMode',
      dataIndex: 'linkMode',
      key: 'linkMode',
      data: DICT_CONSTANTS.LIKE_MODE,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'LIKE_MODE',
      width: 120,
    },
    {
      title: 'dualSwitch',
      dataIndex: 'dualSwitch',
      key: 'dualSwitch',
      data: DICT_CONSTANTS.COMMON_SWITCH,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'COMMON_SWITCH',
      width: 120,
    },
    {
      title: 'recordSwitch',
      dataIndex: 'recordSwitch',
      key: 'recordSwitch',
      data: DICT_CONSTANTS.COMMON_SWITCH,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'COMMON_SWITCH',
      width: 120,
    },
    {
      title: 'func',
      dataIndex: 'func',
      key: 'func',
      width: 150,
    },
    {
      title: 'host',
      dataIndex: 'host',
      key: 'host',
      width: 180,
    },
    {
      width: 180,
      title: 'createTime',
      dataIndex: 'createTime',
      key: 'createTime',
      valueType: RENDER_TYPE.DateTime,
    },
    {
      width: 180,
      title: 'updateTime',
      dataIndex: 'updateTime',
      key: 'updateTime',
      valueType: RENDER_TYPE.DateTime,
    },
  ],
  // 操作列
  optionList: [
    { type: OPERATE_TYPE.detail, permissionId: PERMISSION_CONSTANTS.INTERFACE_MAP.DETAIL },
    { type: OPERATE_TYPE.edit, permissionId: PERMISSION_CONSTANTS.INTERFACE_MAP.EDIT },
    { type: OPERATE_TYPE.delete, permissionId: PERMISSION_CONSTANTS.INTERFACE_MAP.DELETE },
    { type: OPERATE_TYPE.copy, permissionId: PERMISSION_CONSTANTS.INTERFACE_MAP.COPY },
  ],
  // 新增按钮权限
  formActionPermissionObj: {
    create: PERMISSION_CONSTANTS.INTERFACE_MAP.CREATE,
  },
};

// 需要查询的参数类型
export const dictEnum = {
  // msgId: DICT_CONSTANTS.DICT_ENUM_MAP.msgId,
};
