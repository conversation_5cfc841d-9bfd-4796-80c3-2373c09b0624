// 参数管理/接口映射参数应用服务
import { OPERATE_TYPE } from '@/constants/publicConstant';
import React, { Suspense, useState } from 'react';
import { IInterfaceMappingRow } from './IInterfaceMapping';
import { dictEnum, pageConfig } from './pageConfig';
import useFormConfig from './useFormConfig';

const PageTemplate = React.lazy(
  () => import('@/components/templates/PageTemplate'),
);

const interfaceMappingPage: React.FC = () => {
  const {
    prefix,
    urlObj,
    cardTitle,
    resetValue,
    searchSource,
    columns,
    optionList,
    formActionPermissionObj,
  } = pageConfig;
  const { setEditType, infoFormConfig } = useFormConfig();
  const [detailData, setDetailData] = useState<IInterfaceMappingRow>({});

  // 查询条件配置
  const searchConfig = {
    searchSource,
    searchValue: { ...resetValue },
    resetValue,
  };

  // 自定义操作列安娜操作列
  const getOptions = (row: any) => {
    return [
      { type: OPERATE_TYPE.copy, disabled: row?.linkMode === '2' },
      { type: OPERATE_TYPE.detail },
      { type: OPERATE_TYPE.edit },
      { type: OPERATE_TYPE.delete },
    ];
  };

  // 操作栏配置
  const formActionConfig = { permissionObj: formActionPermissionObj };

  // 表格配置
  const tableConfig = {
    columns,
    optionList,
    rowKey: 'paramIndex',
    showPagination: true,
    getOptions,
    props: { scroll: { x: 1500, y: 2000 } },
  };

  // 表单配置
  const formConfig = {
    config: infoFormConfig,
    data: detailData,
    intlPrefix: prefix,
    onChange: () => {},
  };

  // 列表按钮操作
  const handleAction = (editType: string, row: any) => {
    setDetailData({ ...row });
    setEditType(editType);
    switch (editType) {
      case OPERATE_TYPE.copy:
        setDetailData({ ...row, dualSwitch: '0', url: '', host: '' });
        break;
    }
  };

  return (
    <Suspense>
      <PageTemplate
        searchConfig={searchConfig}
        tableConfig={tableConfig}
        formConfig={formConfig}
        urlObj={urlObj}
        formActionConfig={formActionConfig}
        cardTitle={cardTitle}
        intlPrefix={prefix}
        dictEnum={dictEnum}
        onAction={handleAction}
      />
    </Suspense>
  );
};
export default React.memo(interfaceMappingPage);
