import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import { useState } from 'react';

const useFormConfig = () => {
  const [editType, setEditType] = useState<string>(OPERATE_TYPE.list);

  // 表单字段
  const infoFormConfig = [
    {
      type: 'FormHearder',
      title: 'baseInfo',
    },
    {
      type: 'Row',
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'msgId',
          label: 'msgId',
          rules: [{ required: true }],
          disabled: editType === OPERATE_TYPE.copy,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'sourceChannel',
          label: 'sourceChannel',
          rules: [{ required: true }],
          disabled: editType === OPERATE_TYPE.copy,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'url',
          label: 'url',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'module',
          label: 'module',
          rules: [{ required: true }],
          disabled: editType === OPERATE_TYPE.copy,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'protocol',
          label: 'protocol',
          rules: [{ required: true }],
          showKey: false,
          isint: '0',
          data: DICT_CONSTANTS.PROTOCOL,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'linkMode',
          label: 'linkMode',
          rules: [{ required: true }],
          data: DICT_CONSTANTS.LIKE_MODE,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'dualSwitch',
          label: 'dualSwitch',
          rules: [{ required: true }],
          data: DICT_CONSTANTS.COMMON_SWITCH,
          disabled: editType === OPERATE_TYPE.copy,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'recordSwitch',
          label: 'recordSwitch',
          rules: [{ required: true }],
          data: DICT_CONSTANTS.COMMON_SWITCH,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'host',
          label: 'host',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'func',
          label: 'func',
        },
      ],
    },
  ];

  return {
    setEditType,
    infoFormConfig,
  };
};

export default useFormConfig;
