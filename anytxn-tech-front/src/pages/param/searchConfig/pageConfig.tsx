import DICT_CONSTANTS from '@/constants/dictConstants';
import PERMISSION_CONSTANTS from '@/constants/permissionConstants';
import {
  COMPONENT_TYPE,
  I18N_COMON_PAGENAME,
  OPERATE_TYPE,
  RENDER_TYPE,
} from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { IPageConfig } from '@/types/ICommon';

const getRequestData = (postData: any, type: string) => {
  let data = {};
  switch (type) {
    case OPERATE_TYPE.create:
    case OPERATE_TYPE.edit:
      data = { ...postData.formData, type };
      break;
    case OPERATE_TYPE.delete:
      data = { type, incId: postData.incId };
      break;
    default:
      return { ...postData };
  }
  return { ...data };
};

export const pageConfig: IPageConfig = {
  prefix: I18N_COMON_PAGENAME.PARAM,
  cardTitle: 'searchConfig',
  // 页面接口请求
  urlObj: {
    list: urlConstants.SEARCH_CONFIG.LIST,
    create: urlConstants.SEARCH_CONFIG.CREATE,
    edit: urlConstants.SEARCH_CONFIG.EDIT,
    delete: urlConstants.SEARCH_CONFIG.DELETE,
    getRequestData,
    
  },
  resetValue: { key: '', value: '', status: null },
  // 搜索条件字段
  searchSource: [
    {
      value: 'key',
      label: 'searchConfigKey',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'value',
      label: 'searchConfigValue',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'status',
      label: 'status',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.PARAM_STATUS,
    },
  ],

  // 列表字段
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      width: 80,
    },
    {
      title: 'searchConfigKey',
      dataIndex: 'key',
      key: 'key',
      width: 120,
    },
    {
      title: 'searchConfigValue',
      dataIndex: 'value',
      key: 'value',
      width: 150,
    },
    {
      title: 'configDesc',
      dataIndex: 'configDesc',
      key: 'configDesc',
      width: 150,
    },
    {
      width: 100,
      key: 'status',
      title: 'status',
      dataIndex: 'status',
      data: DICT_CONSTANTS.PARAM_STATUS,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'PARAM_STATUS',
    },
    {
      title: 'createTime',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
      valueType: RENDER_TYPE.DateTime,
    },
    {
      width: 150,
      title: 'updateTime',
      dataIndex: 'updateTime',
      key: 'updateTime',
      valueType: RENDER_TYPE.DateTime,
    },
  ],

  // 表单字段
  infoFormConfig: [
    {
      type: 'FormHearder',
      title: 'baseInfo',
    },
    {
      type: 'Row',
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'key',
          label: 'searchConfigKey',
          maxLength: 14,
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'value',
          label: 'searchConfigValue',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'status',
          label: 'status',
          rules: [{ required: true }],
          data: DICT_CONSTANTS.PARAM_STATUS,
          // disabled: true,
        },
        {
          type: COMPONENT_TYPE.TEXTAREA,
          name: 'configDesc',
          label: 'configDesc',
          rules: [{ required: true }],
        },
      ],
    },
  ],
  // 操作列
  optionList: [
    { type: OPERATE_TYPE.detail, permissionId: PERMISSION_CONSTANTS.CONFIG_PARAM.DETAIL },
    { type: OPERATE_TYPE.edit, permissionId: PERMISSION_CONSTANTS.CONFIG_PARAM.EDIT },
    { type: OPERATE_TYPE.delete, permissionId: PERMISSION_CONSTANTS.CONFIG_PARAM.DELETE },
  ],
  // 新增按钮权限
  formActionPermissionObj: {
    create: PERMISSION_CONSTANTS.CONFIG_PARAM.CREATE,
  },
};

// 需要查询的参数类型
export const dictEnum = {
  // msgId: DICT_CONSTANTS.DICT_ENUM_MAP.msgId,
};
