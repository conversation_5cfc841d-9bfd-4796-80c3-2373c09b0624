// 参数管理/接口配置参数应用服务
import React, { Suspense, useState } from 'react';
import { IInterfaceConfigRow } from './IInterfaceConfig';
import { dictEnum, pageConfig } from './pageConfig';
import { 
  Button, 
  Card, 
  CommonModal, 
  TabDescription, 
  GradientButton, 
  Search, 
  CommonTable, 
  EditTable, 
  UploadCom,
} from 'my-custom-ui-components';
import { PlusOutlined } from '@ant-design/icons';

const PageTemplate = React.lazy(
  () => import('@/components/templates/PageTemplate'),
);

const InterfaceConfigPage: React.FC = () => {
  const {
    prefix,
    urlObj,
    cardTitle,
    resetValue,
    searchSource,
    columns,
    optionList,
    formActionPermissionObj,
    infoFormConfig,
  } = pageConfig;
  const [detailData, setDetailData] = useState<IInterfaceConfigRow>({});

  // 查询条件配置
  const searchConfig = {
    searchSource: searchSource,
    searchValue: { ...resetValue },
    resetValue,
  };

  // 操作栏配置
  const formActionConfig = { permissionObj: formActionPermissionObj };

  // 表格配置
  const tableConfig = {
    columns,
    optionList,
    rowKey: 'paramIndex',
    showPagination: true,
  };

  // 表单配置
  const formConfig = {
    config: infoFormConfig,
    data: detailData,
    intlPrefix: prefix,
    onChange: () => {},
  };
  // 列表按钮操作
  const handleAction = (editType: string, row: IInterfaceConfigRow) => {
    setDetailData({ ...row });
  };
 
  return (
    <Suspense>
   
          <GradientButton
            color={'#1890FF'}
            type="primary"
            size="middle"
            icon={<PlusOutlined />}
            style={{ marginBottom: 10 }}
            onClick={() => {  }}
          >
            {'新增'}
          </GradientButton>
          
      <Search
        searchValue={resetValue}
        resetValue={resetValue}
        intlPrefix={prefix}
        searchSource={searchSource}
        onSearch={(values: any) => {
          console.log(values);
        }}
      />
   
      <PageTemplate
        searchConfig={searchConfig}
        tableConfig={tableConfig}
        formConfig={formConfig}
        urlObj={urlObj}
        formActionConfig={formActionConfig}
        cardTitle={cardTitle}
        intlPrefix={prefix}
        dictEnum={dictEnum}
        onAction={handleAction}
      />
    </Suspense>
  );
};
export default React.memo(InterfaceConfigPage);
