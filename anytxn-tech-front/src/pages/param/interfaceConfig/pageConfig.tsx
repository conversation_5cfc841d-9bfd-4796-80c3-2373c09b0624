import DICT_CONSTANTS from '@/constants/dictConstants';
import PERMISSION_CONSTANTS from '@/constants/permissionConstants';
import {
  COMPONENT_TYPE,
  I18N_COMON_PAGENAME,
  OPERATE_TYPE,
  RENDER_TYPE,
} from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { IPageConfig } from '@/types/ICommon';

// 组装增删改查请求参数
const getRequestData = (postData: any, type: string) => {
  let data = {};
  switch (type) {
    case OPERATE_TYPE.create:
    case OPERATE_TYPE.edit:
      data = { ...postData.formData, type };
      break;
    case OPERATE_TYPE.delete:
      data = { type, incId: postData.incId };
      break;
    default:
      return { ...postData };
  }
  return { ...data };
};

export const pageConfig: IPageConfig = {
  prefix: I18N_COMON_PAGENAME.PARAM,
  cardTitle: 'interfaceConfig',
  // 页面接口请求
  urlObj: {
    list: urlConstants.INTERFACE_CONFIG.LIST,
    create: urlConstants.INTERFACE_CONFIG.CREATE,
    edit: urlConstants.INTERFACE_CONFIG.EDIT,
    delete: urlConstants.INTERFACE_CONFIG.DELETE,
    getRequestData,
    
  },
  resetValue: { clusterGroup: '', msgId: '', paramSts: null },
  // 搜索条件字段
  searchSource: [
    {
      value: 'clusterGroup',
      label: 'clusterGroup',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'msgId',
      label: 'msgId',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'interfaceType',
      label: 'interfaceType',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.INTER_FACE_TYPE,
    },
    {
      value: 'status',
      label: 'status',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.PARAM_STATUS,
    },
  ],

  // 列表字段
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      width: 80,
    },
    {
      title: 'msgId',
      dataIndex: 'msgId',
      key: 'msgId',
      width: 120,
    },
    {
      title: 'clusterGroup',
      dataIndex: 'clusterGroup',
      key: 'clusterGroup',
      width: 120,
    },
    {
      title: 'module',
      dataIndex: 'module',
      key: 'module',
      width: 150,
    },
    {
      width: 100,
      key: 'status',
      title: 'status',
      dataIndex: 'status',
      data: DICT_CONSTANTS.PARAM_STATUS,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'PARAM_STATUS',
    },
    {
      width: 100,
      key: 'interfaceType',
      title: 'interfaceType',
      dataIndex: 'interfaceType',
      data: DICT_CONSTANTS.INTER_FACE_TYPE,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'INTER_FACE_TYPE',
    },
    {
      width: 180,
      title: 'createTime',
      dataIndex: 'createTime',
      key: 'createTime',
      valueType: RENDER_TYPE.DateTime,
    },
    {
      width: 180,
      title: 'updateTime',
      dataIndex: 'updateTime',
      key: 'updateTime',
      valueType: RENDER_TYPE.DateTime,
    },
  ],

  // 表单字段
  infoFormConfig: [
    {
      type: 'FormHearder',
      title: 'baseInfo',
    },
    {
      type: 'Row',
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'msgId',
          label: 'msgId',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'clusterGroup',
          label: 'clusterGroup',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'module',
          label: 'module',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'interfaceType',
          label: 'interfaceType',
          rules: [{ required: true }],
          data: DICT_CONSTANTS.INTER_FACE_TYPE,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'status',
          label: 'status',
          rules: [{ required: true }],
          data: DICT_CONSTANTS.PARAM_STATUS,
          // disabled: true,
        },
      ],
    },
  ],
  // 操作列
  optionList: [
    { type: OPERATE_TYPE.detail, permissionId: PERMISSION_CONSTANTS.INTERFACE_CONFIG.DETAIL },
    { type: OPERATE_TYPE.edit, permissionId: PERMISSION_CONSTANTS.INTERFACE_CONFIG.EDIT },
    { type: OPERATE_TYPE.delete, permissionId: PERMISSION_CONSTANTS.INTERFACE_CONFIG.DELETE },
  ],
  // 新增按钮权限
  formActionPermissionObj: {
    create: PERMISSION_CONSTANTS.INTERFACE_CONFIG.CREATE,
  },
};

// 需要查询的参数类型
export const dictEnum = {
  // msgId: DICT_CONSTANTS.DICT_ENUM_MAP.msgId,
};
