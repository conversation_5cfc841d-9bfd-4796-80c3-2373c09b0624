import PERMISSION_CONSTANTS from '@/constants/permissionConstants';
import {
  COMPONENT_TYPE,
  I18N_COMON_PAGENAME,
  OPERATE_TYPE,
} from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { IPageConfig } from '@/types/ICommon';

const getRequestData = (postData: any, type: string) => {
  let data = {};
  switch (type) {
    case OPERATE_TYPE.create:
    case OPERATE_TYPE.edit:
      data = {
        ...postData.formData,
        type,
      };
      break;
    case OPERATE_TYPE.delete:
      data = { type, incId: postData.incId };
      break;
    default:
      return { ...postData };
  }
  return { ...data };
};

const validateJson = (rule: any, value: string) => {
  // const jsonRegex = /^\[\{(?:\s*"[^"]*"\s*:\s*"[^"]*"\s*,?)*\}\]$/; // 简单的 JSON 正则表达式
  const jsonRegex =
    /^\[\{("[a-zA-Z_][a-zA-Z0-9_]*":("[^"]*"|\d+|true|false|null|(\[.*?\]|{.*?}))*)(,\s*("[a-zA-Z_][a-zA-Z0-9_]*":("[^"]*"|\d+|true|false|null|(\[.*?\]|{.*?})))*\s*)*\}\]$/;
  if (value && !jsonRegex.test(value.replace(/\s+/g, ''))) {
    return Promise.reject('請輸入有效的 JSON 字符串');
  }
  return Promise.resolve();
};

export const pageConfig: IPageConfig = {
  prefix: I18N_COMON_PAGENAME.PARAM,
  cardTitle: 'converConfig',
  // 页面接口请求
  urlObj: {
    list: urlConstants.CONVER_CONFIG.LIST,
    create: urlConstants.CONVER_CONFIG.CREATE,
    edit: urlConstants.CONVER_CONFIG.EDIT,
    delete: urlConstants.CONVER_CONFIG.DELETE,
    getRequestData,
  },

  resetValue: { url: '', configType: '' },
  // 搜索条件字段
  searchSource: [
    {
      value: 'url',
      label: 'converConfigUrl',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'configType',
      label: 'configType',
      type: COMPONENT_TYPE.INPUT,
    },
    // {
    //   value: 'inConfigContext',
    //   label: 'inConfigContext',
    //   type: COMPONENT_TYPE.INPUT,
    // },
    // {
    //   value: 'outConfigContext',
    //   label: 'outConfigContext',
    //   type: COMPONENT_TYPE.INPUT,
    // },
  ],

  // 列表字段
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      width: 80,
    },
    {
      title: 'converConfigUrl',
      dataIndex: 'url',
      key: 'url',
      width: 120,
    },
    {
      title: 'configType',
      dataIndex: 'configType',
      key: 'configType',
      width: 150,
    },
    // {
    //   width: 100,
    //   key: 'inConfigContext',
    //   title: 'inConfigContext',
    //   dataIndex: 'inConfigContext',
    // },
    // {
    //   width: 100,
    //   key: 'outConfigContext',
    //   title: 'outConfigContext',
    //   dataIndex: 'outConfigContext',
    // },
  ],

  // 表单字段
  infoFormConfig: [
    {
      type: 'FormHearder',
      title: 'baseInfo',
    },
    {
      type: 'Row',
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'url',
          label: 'converConfigUrl',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'configType',
          label: 'configType',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.TEXTAREA,
          name: 'inConfigContext',
          label: 'inConfigContext',
          rules: [{ required: true, validator: validateJson }],
          autoSize: true,
          // disabled: true,
        },
        {
          type: COMPONENT_TYPE.TEXTAREA,
          name: 'outConfigContext',
          label: 'outConfigContext',
          rules: [{ required: true, validator: validateJson }],
          autoSize: true,
          // disabled: true,
        },
      ],
    },
  ],
  // 操作列
  optionList: [
    {
      type: OPERATE_TYPE.detail,
      permissionId: PERMISSION_CONSTANTS.CONVER_CONFIG.DETAIL,
    },
    {
      type: OPERATE_TYPE.edit,
      permissionId: PERMISSION_CONSTANTS.CONVER_CONFIG.EDIT,
    },
    {
      type: OPERATE_TYPE.delete,
      permissionId: PERMISSION_CONSTANTS.CONVER_CONFIG.DELETE,
    },
  ],
  // 新增按钮权限
  formActionPermissionObj: {
    create: PERMISSION_CONSTANTS.CONVER_CONFIG.CREATE,
  },
};

// 需要查询的参数类型
export const dictEnum = {
  // msgId: DICT_CONSTANTS.DICT_ENUM_MAP.msgId,
};
