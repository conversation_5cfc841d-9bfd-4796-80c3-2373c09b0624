// 参数管理/接口映射参数应用服务
import { OPERATE_TYPE } from '@/constants/publicConstant';
import React, { Suspense, useState } from 'react';
import { IConvertConfig } from './IConvertConfig';
import { dictEnum, pageConfig } from './pageConfig';

const PageTemplate = React.lazy(
  () => import('@/components/templates/PageTemplate'),
);

const ConvertConfigPage: React.FC = () => {
  const {
    prefix,
    urlObj,
    cardTitle,
    resetValue,
    searchSource,
    columns,
    optionList,
    formActionPermissionObj,
    infoFormConfig,
  } = pageConfig;
  const [detailData, setDetailData] = useState<IConvertConfig>({});

  // 查询条件配置
  const searchConfig = {
    searchSource,
    searchValue: { ...resetValue },
    resetValue,
  };

  // 操作栏配置
  const formActionConfig = { permissionObj: formActionPermissionObj };

  // 表格配置
  const tableConfig = {
    columns,
    optionList,
    rowKey: 'paramIndex',
    showPagination: true,
  };

  // 表单配置
  const formConfig = {
    config: infoFormConfig,
    data: detailData,
    intlPrefix: prefix,
    onChange: () => {},
  };

  // 列表按钮操作
  const handleAction = (editType: string, row: IConvertConfig) => {
    if ([OPERATE_TYPE.edit, OPERATE_TYPE.detail].includes(editType)) {
      setDetailData({
        ...row,
        inConfigContext: JSON.stringify(
          JSON.parse(row?.inConfigContext || ''),
          null,
          2,
        ),
        outConfigContext: JSON.stringify(
          JSON.parse(row?.outConfigContext || ''),
          null,
          2,
        ),
      });
    } else {
      setDetailData({ ...row });
    }
  };

  return (
    <Suspense>
      <PageTemplate
        searchConfig={searchConfig}
        tableConfig={tableConfig}
        formConfig={formConfig}
        urlObj={urlObj}
        formActionConfig={formActionConfig}
        cardTitle={cardTitle}
        intlPrefix={prefix}
        dictEnum={dictEnum}
        onAction={handleAction}
      />
    </Suspense>
  );
};
export default React.memo(ConvertConfigPage);
