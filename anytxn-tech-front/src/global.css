/* 引入antd样式 */
@import 'antd/dist/reset.css';

/* 引入全局覆盖antd样式 */
@import '@/assets/styles/cover.less';

/* 引入自定义样式 */
@import '@/assets/styles/custom.less';

/* 全局变量 */
:root {
  /* 主题色：在此扩展主题色，并用于主题切换 */
  --color-one: #f6ffed;
  --color-two: #8cd4a4;
  --color-three: #62c082;
  --color-main: #345da7;
  --color-five: #008545ec;
  --color-six: #01723bcf;

  /* 中色系 */
  --gray-light: #fafafa;
  --gray-one: #fff;
  --gray-two: #f5f5f5;
  --gray-three: #d9d9d9;
  --gray-main: #8c8c8c;
  --gray-five: #434343;
  --gray-six: #000;

  /* 提醒色 */
  --warning-color: #faad14;
  --error-color: #f5222d;
  --success-color: #52c41a;

  /* 盒子 */
  --common-border: 0.5px solid var(--color-two);
  --margin-padding: 1rem;
  --margin-padding-s: 0.5rem;
  --margin-padding-l: 1.5rem;

  /* 尺寸 */
  --font-size: 0.8 rem;
  --font-size-s: 0.7rem;
  --font-size-l: 1rem;
  --font-size-xl: 1.2rem;
}

/* 全景样式 */
body {
  margin: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  border-radius: 2px;
  background-color: var(--gray-three);
}

::-webkit-scrollbar-track {
  border-radius: 2px;
  background-color: #fff;
}

::-webkit-scrollbar-thumb {
  border-radius: 2px;
  background-color: var(--gray-three);
}

/* 当前容器 */
#root {
  height: 100vh;
}
