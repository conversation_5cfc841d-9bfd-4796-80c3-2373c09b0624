import { LOCAL, OPERATE_TYPE } from '@/constants/publicConstant';
import { ICommonTableActionItem } from '@/types/ICommon';
import { TNotification, TOptionItem } from '@/types/TCommon';
import {
  CloseOutlined,
  CopyOutlined,
  DeleteOutlined,
  EyeOutlined,
  FormOutlined,
  SaveOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { Button, notification, Popconfirm, Space, Tooltip } from 'antd';
import { ReactNode } from 'react';
import { FormattedMessage, useIntl } from 'umi';
import useAuthButton from './useAuthButton';

const useIntlCustom = () => {
  const intl = useIntl();
  const { hasPermission } = useAuthButton();

  /**
   *
   * @param prefix 前缀
   * @param key 字段id
   * @param values 动态拼接的字段
   * @returns
   */
  const translate = (prefix: string, key: string, values = {}): string => {
    const id = prefix ? `${prefix}.${key}` : key;
    return intl.formatMessage({ id }, values);
  };

  /** 获取通知
   * @param prefix 国际化key
   * @param type 'success' | 'info' | 'warning' | 'error'
   * @param context 通知内容
   * @param duration 选填，设置通知几秒自动关闭
   */
  const openNotificationTip = (
    prefix: string,
    type: TNotification | string,
    context: string,
    duration?: number,
  ) => {
    // @ts-ignore
    notification[type]({
      message: translate('common', 'tip'),
      duration: duration || 3,
      description: translate(prefix, context),
    });
  };

  // 获取标题
  const formatActionTitle = (
    actionType: string,
    prefix: string,
    title: string = '',
  ): string => {
    const actionLabel =
      OPERATE_TYPE[actionType] && actionType !== OPERATE_TYPE.list
        ? translate('common', OPERATE_TYPE[actionType])
        : '';
    const titleLabel = title ? translate(prefix, title) : '';
    return `${actionLabel}${titleLabel}`;
  };

  // 获取操作结果提示
  const getActionResultTitle = (
    title: string,
    prefix: string,
    type: string,
    isSuccess: boolean,
  ) => {
    let suffixName = isSuccess
      ? translate('common', 'success')
      : translate('common', 'fail');
    return formatActionTitle(type, prefix, title) + suffixName;
  };

  // 格式化html中的文本
  const formateHtmlText = (prefix: string, key: string) => {
    const id = prefix ? `${prefix}.${key}` : key;
    return <FormattedMessage id={id} />;
  };

  // 格式化日期
  const formatLocalDate = (date: Date, options: object) => {
    return intl.formatDate(date, options);
  };

  // 格式化数字
  const formatLocalNumber = (value: number | bigint, options: object) => {
    return intl.formatNumber(value, options);
  };

  const theme = localStorage.getItem(LOCAL.THEME) || '';
  // 获取按钮
  const renderButton = (data: {
    type: string;
    disabled?: boolean;
    onClick?: (type: string) => void;
  }) => {
    const { type, disabled = false, onClick = () => {} } = data;
    const title = translate('common', type);
    switch (type) {
      case OPERATE_TYPE.copy:
        return (
          <Tooltip key={type} placement="top" title={title}>
            <Button
              type="link"
              style={{ color: theme }}
              icon={<CopyOutlined />}
              disabled={disabled}
              onClick={() => onClick(type)}
            />
          </Tooltip>
        );
      case OPERATE_TYPE.detail:
        return (
          <Tooltip key={type} placement="top" title={title}>
            <Button
              type="link"
              style={{ color: theme }}
              icon={<EyeOutlined />}
              disabled={disabled}
              onClick={() => onClick(type)}
            />
          </Tooltip>
        );
      case OPERATE_TYPE.edit:
        return (
          <Tooltip key={type} placement="top" title={title}>
            <Button
              type="link"
              style={{ color: theme }}
              icon={<FormOutlined />}
              disabled={disabled}
              onClick={() => onClick(type)}
            />
          </Tooltip>
        );
      case OPERATE_TYPE.delete:
        return (
          <Tooltip key={type} placement="top" title={title}>
            <Popconfirm
              title={translate('common', 'confirmDelete')}
              okText={translate('common', 'confirm')}
              cancelText={translate('common', 'cancel')}
              onConfirm={() => onClick(type)}
            >
              <Button
                type="link"
                icon={<DeleteOutlined />}
                disabled={disabled}
                danger
              />
            </Popconfirm>
          </Tooltip>
        );
      case OPERATE_TYPE.cancel:
        return (
          <Tooltip key={type} placement="top" title={title}>
            <Button
              type="link"
              icon={<CloseOutlined />}
              onClick={() => onClick(type)}
            />
          </Tooltip>
        );
      case OPERATE_TYPE.save:
        return (
          <Tooltip key={type} placement="top" title={title}>
            <Button
              type="link"
              icon={<SaveOutlined />}
              onClick={() => onClick(type)}
            />
          </Tooltip>
        );
      default:
        return (
          <Tooltip title={translate('common', 'search')}>
            <Button shape="circle" icon={<SearchOutlined />} />
          </Tooltip>
        );
    }
  };

  /**
   * 渲染表格的操作列
   * @param data 操作列配置
   * @param onClick 事件回调
   * @returns {JSX}
   */
  const renderActionColumn = (
    data: Array<string | ICommonTableActionItem> = [],
    onClick = (type: any) => {},
  ): ReactNode | null => {
    if (data && data.length > 0) {
      const btns: Array<ReactNode> = [];
      // console.log('3333--data--permissionId', data);
      data.forEach((item: ICommonTableActionItem | string) => {
        // 每种操作类型的属性提取出来
        let type;
        let title;
        let disabled = false;
        let icon;
        let permissionId;
        if (typeof item === 'object') {
          type = item.type;
          title = item.title || item.type;
          title = item.prefix && title ? translate(item.prefix, title) : title;
          disabled = item?.disabled || false;
          icon = item?.icon;
          permissionId = item.permissionId;
        } else if (typeof item === 'string') {
          type = item;
        }
        // console.log('3333----permissionId', permissionId, hasPermission(permissionId));
        
        // 有传permissionId且无权限则不渲染
        if (permissionId && !hasPermission(permissionId)) {
          return;
        }
        // 判断是否有icon
        if (icon) {
          btns.push(
            <Tooltip key={type} placement="top" title={title}>
              <Button
                type="link"
                icon={icon}
                disabled={disabled ?? false}
                style={{ color: theme }}
                onClick={() => onClick(item)}
              />
            </Tooltip>,
          );
        } else if (type) {
          btns.push(renderButton({ type, disabled, permissionId, onClick }));
        }
      });
      return <Space>{btns}</Space>;
    } else {
      return null;
    }
  };

  /**
   * 返回下拉框选项数据
   * @param data [{key,value}]
   * @param showKey
   * @param prefix
   * @returns {*[]}
   */
  const getSelectOption = (
    data: Array<TOptionItem>,
    showKey: boolean = true,
    prefix: string = '',
  ) => {
    const translateSelectDict = (data: any) => {
      return data.map((item: any) => {
        const label = prefix ? translate(prefix, item.value) : item.value;
        const translatedNode = {
          ...item,
          key: item.key,
          value: item.key,
          label: showKey && item.key ? `${item.key} - ${label}` : label,
        };
        // 递归处理子节点
        if (item.children && item.children.length > 0) {
          translatedNode.children = translateSelectDict(item.children);
        }
        return translatedNode;
      });
    };
    if (data && data.length > 0) {
      return translateSelectDict(data);
    }
  };
  /**
   * 返回可编辑表格下拉框选项数据
   * @param data [{key,value}]
   * @param showKey
   * @param prefix
   * @returns {*[]}
   */
  const getEditTableSelectOption = (
    data: Array<TOptionItem>,
    showKey: boolean = true,
    prefix: string = '',
  ): object => {
    const res: any = {};
    data.forEach((item) => {
      const label = prefix ? translate(prefix, item.value) : item.value;
      res[item.key] = {
        text: showKey && item.key ? `${item.key} - ${label}` : label,
      };
    });
    return res;
  };

  const defaultInputPlaceholder = translate('common', 'inputPlaceholder');
  const defaultSelectPlaceholder = translate('common', 'selectPlaceholder');

  return {
    translate,
    formatActionTitle,
    formateHtmlText,
    formatLocalDate,
    formatLocalNumber,
    renderButton,
    renderActionColumn,
    getSelectOption,
    getActionResultTitle,
    openNotificationTip,
    defaultInputPlaceholder,
    defaultSelectPlaceholder,
    getEditTableSelectOption,
  };
};

export default useIntlCustom;
