/**
 * @description 页面按钮权限 hooks
 * */
import { useModel } from 'umi';

const useAuthButton = () => {
  const { initialState } = useModel('@@initialState') as any;
  const { menuData } = initialState || {};
  const buttonData = menuData?.filter((item: any) => item.menuType === '1');
  // 判断按钮是否有权限
  const hasPermission = (permissionId: string): boolean => {
    // console.log('3333---permissionId', permissionId, buttonData);
    // 权限数据是否已初始化完成
    if (buttonData) {
      for (const item of buttonData) {
        // 取store里的有权限按钮数据
        if (permissionId === item.permissionId) {
          return true;
        }
      }
    }
    return false;
  };

  return {
    hasPermission,
  };
};

export default useAuthButton;
