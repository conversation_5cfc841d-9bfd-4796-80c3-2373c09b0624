/**
 * 网络请求
 * https://github.com/umijs/umi-request/blob/master/README_zh-CN.md
 */
import { notification } from 'antd';
import BigJSON from 'json-bigint';
import type { RequestOptionsInit } from 'umi-request';
import request, { extend } from 'umi-request';
import { getHeaders } from './header';
const JSONbigString = BigJSON({
  storeAsString: true,
});
request.interceptors.request.use((url: string, options: RequestOptionsInit) => {
  options.headers = {
    ...options.headers,
    ...getHeaders(),
  };
  return {
    url,
    options,
  };
});

request.interceptors.response.use(async (response: any, options: any) => {
  const res = await response.clone().json();
  const { header, data = {}, message } = res || {};
  // 有返回码的普通请求
  if (header?.errorCode) {
    // 接口错误
    if (header.errorCode !== '000000') {
      notification.error({ message: header.errorMsg });
      return false;
    }
    try {
      const resText = await response.clone().text();
      return JSONbigString.parse(resText);
    } catch (err) {
      notification.error({ message: '接口回應異常' });
    }
  } else {
    // 没返回码的特殊请求
    message && notification.error({ message });
    return res;
  }
});

export const dataRequest = extend({
  prefix: '/api/anytxn-tech-web/',
});

export default request;
