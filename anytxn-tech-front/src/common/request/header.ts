import { SESSION } from '@/constants/publicConstant';

let tokenVal: any = sessionStorage.getItem(SESSION.token);
if (window.location.search?.includes('token')) {
  tokenVal = window.location.search?.split('?')?.[1]?.split('=')?.[1];
}
sessionStorage.setItem(SESSION.token, tokenVal);
const token = sessionStorage.getItem(SESSION.token);
export const getToken = () => {
  return tokenVal || token;
};

export const getHeaders = () => {
  const headers: any = {};
  const token = getToken();

  if (token) {
    headers.Authorization = ['Bearer', token].join(' ');
  }
  // headers.channel = 'T_P';
  // headers['x-requested-with'] = 'XMLHttpRequest';
  return headers;
};
