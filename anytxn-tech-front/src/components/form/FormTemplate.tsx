import _ from 'lodash';
import React, { forwardRef, ReactNode, useImperativeHandle } from 'react';
// import store from '@/store';
import useIntlCustom from '@/common/hooks/useIntlCustom';
import { EditTable, FormHearder, FormMaintenance } from '@/components';
import {
  COMPONENT_TYPE,
  DATE_FORMATE,
  DEFAULT_AMOUNT_PROPS,
  FORMITEM_TYPE,
  NOT_BELONG_COMPONENT,
} from '@/constants/publicConstant';
import styleConstant from '@/constants/styleConstant';
import { TUnknownProperties } from '@/types/ICommon';
import {
  formatformItemData,
  formatformItemNormalize,
  formatterAmountInput,
  parserAmountInput,
} from '@/utils/comUtil';
import {
  Button,
  Calendar,
  Cascader,
  Checkbox,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
  Skeleton,
  Space,
  Tree,
  TreeSelect,
} from 'antd';
import { useForm } from 'antd/es/form/Form';
import { IFormTemplateProps, PropsType } from './types/IFormTemplate';

const FormTemplate: React.FC<IFormTemplateProps> = forwardRef(
  (
    {
      formRef, // 表单dom节点
      config, // 配置数据
      initialData = {}, // 绑定数据
      loading = false, // 是否加载
      canEdit = true, // 是否可以编辑
      showMaintenance = true, // 是否显示维护信息
      className = '', // 样式类名，比如 app-overflow
      formLayout = styleConstant.formLayout2, // 样式布局
      intlPrefix,
      editTableRefList = {},
      onChange = () => {},
      onExpand = () => {},
      onCheck = () => {},
      onLoad = () => {},
      onSelect = () => {},
      onSearch = () => {},
    },
    ref,
  ) => {
    // hooks变量
    const {
      translate,
      getSelectOption,
      defaultInputPlaceholder,
      defaultSelectPlaceholder,
    } = useIntlCustom();
    const [form] = useForm();
    // const [dictState] = store.useModel('dict');
    // 把当前form显示暴露给父组件
    useImperativeHandle(ref, () => {
      return {
        async onSubmit() {
          try {
            const res: any = { formData: {} };
            let flag = true;
            const formData = await form.validateFields();
            if (formData) {
              res.formData = formData;
            } else {
              flag = false;
            }
            // 可编辑表格数据
            for (const tableName in editTableRefList) {
              const tableRef = editTableRefList[tableName];
              if (tableRef.current) {
                const data = await tableRef.current.validateFields();
                // 返回错误码
                if (data.errorCode) {
                  return data;
                } else if (data) {
                  res[tableName] = data;
                } else {
                  flag = false;
                }
              } else {
                flag = false;
              }
            }
            return flag ? res : null;
          } catch (error) {
            return null;
          }
        },
        onReset() {
          form.resetFields();
          for (const tableName in editTableRefList) {
            const tableRef = editTableRefList[tableName];
            if (tableRef.current) {
              tableRef.current.resetFields();
            }
          }
        },
      };
    });

    // 一行两列
    const renderTwoFields = (data: any) => {
      const res: Array<ReactNode> = [];
      if (data && data.length > 0) {
        data.forEach((item: any, index: number) => {
          const com = renderOneFields(item);
          const { name, label, rules, prefix, format } = item;
          const formItemProp = {
            name,
            label: translate(prefix || intlPrefix, label ?? ''),
            rules,
          };
          if (item.formlayoutType === 'one') {
            res.push(
              <Col key={index} span={24}>
                <Form.Item
                  {...formItemProp}
                  {...styleConstant.formLayout3}
                  getValueProps={(value) => ({
                    value: formatformItemData(item.type, value),
                  })}
                  normalize={(value) =>
                    formatformItemNormalize(item.type, value, format)
                  }
                >
                  {com}
                </Form.Item>
              </Col>,
            );
          } else {
            res.push(
              <Col span={11} offset={1} key={`right-${index}`}>
                <Form.Item
                  {...formItemProp}
                  getValueProps={(value) => ({
                    value: formatformItemData(item.type, value),
                  })}
                  normalize={(value) =>
                    formatformItemNormalize(item.type, value, format)
                  }
                >
                  {com}
                </Form.Item>
              </Col>,
            );
          }
        });
      }
      return res;
    };

    // form 真正需要渲染的组件
    const renderOneFields = (data: any) => {
      // 遍历剔除不属于组件的属性
      const props: PropsType = { disabled: false, style: {} };
      // 获取不属于组件的属性的值
      const notBelongArray = Object.values(NOT_BELONG_COMPONENT);
      for (const p in data) {
        if (!notBelongArray.includes(p)) {
          props[p] = data[p];
        }
      }
      // 详情页面或者传入值
      props.disabled = props.disabled || !canEdit;
      const { type, label, format, isint, showKey, name } = data;
      // 国际化前缀，可以使用自定义模块
      const prefix = data.prefix || intlPrefix;
      // 是否需要国际化，默认需要
      let isInt = !(isint && isint === '0');
      const tempProps: TUnknownProperties = {
        ...props,
      };
      tempProps.style = { width: props?.width || styleConstant.width100.width };
      let formComResult: any;
      switch (type) {
        case COMPONENT_TYPE.INPUT:
          formComResult = (
            <Input
              placeholder={defaultInputPlaceholder}
              {...tempProps}
              showCount={!!props.maxLength}
            />
          );
          break;
        case COMPONENT_TYPE.SEARCH:
          formComResult = (
            <Input.Search
              placeholder={defaultInputPlaceholder}
              onSearch={(e) => handleSearch(e, form)}
              {...tempProps}
              enterButton={translate(intlPrefix, data.enterButton)}
            />
          );
          break;
        case COMPONENT_TYPE.EXTARA_BTN_INPUT:
          formComResult = (
            <Space.Compact style={styleConstant.width100}>
              <Input
                placeholder={defaultInputPlaceholder}
                {...tempProps}
                showCount={!!props.maxLength}
              />
              <Button
                type="primary"
                onClick={() => onChange?.({ [label]: true })}
              >
                {translate(intlPrefix, data.enterButton)}
              </Button>
            </Space.Compact>
          );
          break;
        case COMPONENT_TYPE.TEXTAREA:
          formComResult = (
            <Input.TextArea
              placeholder={defaultInputPlaceholder}
              {...tempProps}
              showCount={!!props.maxLength}
            />
          );
          break;
        case COMPONENT_TYPE.INPUT_NUMBER:
          formComResult = (
            <InputNumber placeholder={defaultInputPlaceholder} {...tempProps} />
          );
          break;
        case COMPONENT_TYPE.AMOUNT_INPUT: {
          const tempAmountProps: TUnknownProperties = {
            ...DEFAULT_AMOUNT_PROPS,
            ...tempProps,
            formatter: (value: string | number) =>
              formatterAmountInput(value, DEFAULT_AMOUNT_PROPS.decimal),
            parser: (value: string | number) => parserAmountInput(value),
          };
          formComResult = (
            <InputNumber
              placeholder={defaultInputPlaceholder}
              {...tempAmountProps}
            />
          );
          break;
        }
        case COMPONENT_TYPE.RADIO: {
          const options = isInt
            ? getSelectOption(data?.data, showKey, prefix)
            : data?.data;
          formComResult = (
            <Radio.Group
              options={options}
              name={name}
              {...props}
              onChange={(e) => handleRadioChange(e, form)}
            />
          );
          break;
        }
        case COMPONENT_TYPE.TREE:
          onLoad(form);
          formComResult = (
            <Tree
              {...props}
              treeData={data.data}
              onExpand={(expandedKeys) => handleTreeExpand(expandedKeys)}
              onCheck={(key) => handleTreeCheck(key)}
              onSelect={(key, info) => handleTreeSelect(key, info)}
            />
          );
          break;
        case COMPONENT_TYPE.TREE_SELECT:
          formComResult = <TreeSelect {...props} treeData={data.data} />;
          break;
        case COMPONENT_TYPE.CHECK_BOX:
          formComResult = <Checkbox.Group options={data.data} />;
          break;
        case COMPONENT_TYPE.SELECT:
          {
            let selectSource = [];
            let isDict = false;
            // 有传枚举
            if (data?.data) {
              selectSource = data.data;
            } else {
              // 查字典或参数枚举
              // selectSource = dictState.dictMap[data.name];
              isDict = true;
            }
            formComResult = (
              <Select
                placeholder={defaultSelectPlaceholder}
                {...tempProps}
                options={
                  isInt && !isDict
                    ? getSelectOption(selectSource, data.showKey, prefix)
                    : selectSource
                }
                getPopupContainer={(triggerNode) => triggerNode.parentNode}
              />
            );
          }
          break;
        case COMPONENT_TYPE.CASCADER:
          formComResult = (
            <Cascader
              placeholder={defaultSelectPlaceholder}
              {...tempProps}
              options={getSelectOption(data?.data, data.showKey, prefix)}
              getPopupContainer={(triggerNode) => triggerNode.parentNode}
            />
          );
          break;
        case COMPONENT_TYPE.DATE_PICKER:
          formComResult = (
            <DatePicker {...tempProps} format={format || DATE_FORMATE} />
          );
          break;
        case COMPONENT_TYPE.RANGE_PICKER:
          formComResult = <DatePicker.RangePicker {...tempProps} />;
          break;
        default:
          formComResult = null;
          break;
      }
      return formComResult;
    };

    // 渲染整个表单组件
    const renderFormItem = (data: any, index: number) => {
      let formItemResult: any;
      switch (data.type) {
        case FORMITEM_TYPE.FormHearder: // 弹层标题
          formItemResult = (
            <FormHearder
              key={index}
              title={translate(data.prefix || intlPrefix, data.title ?? '')}
            />
          );
          break;
        case FORMITEM_TYPE.Row: // 表单布局，一行两列
          formItemResult = <Row key={index}>{renderTwoFields(data.data)}</Row>;
          break;
        case FORMITEM_TYPE.EditTable: // 可编辑表格
          formItemResult = (
            <EditTable
              key={index}
              {...data}
              intlPrefix={intlPrefix}
              ref={data.editableFormRef}
            />
          );
          break;
        case FORMITEM_TYPE.Calendar: // 日历组件
          formItemResult = (
            <Form.Item
              key={index}
              name={data.name}
              {...styleConstant.formLayout4}
            >
              <Calendar
                onSelect={(e, { source }) =>
                  handleCalSelect(e, { source }, form)
                }
                {...data.data}
              />
            </Form.Item>
          );
          break;
        case FORMITEM_TYPE.Single: // 一行一列
          const { data: formData } = data;
          if (Array.isArray(formData) && formData?.length) {
            formItemResult = (
              <>
                {formData.map((formItemData, formIndex) => {
                  const { name, label, rules } = formItemData;
                  const formItemProp = {
                    name,
                    label: translate(intlPrefix, label),
                    rules,
                  };
                  return (
                    <Form.Item
                      key={`single-${index}-${formIndex}`}
                      {...styleConstant.formLayout}
                      {...formItemProp}
                    >
                      {renderOneFields(formItemData)}
                    </Form.Item>
                  );
                })}
              </>
            );
          }
          break;
        default:
          formItemResult = null;
          break;
      }
      return formItemResult;
    };

    const handleRadioChange = (e, form) => {
      if (onChange) {
        const obj = { e, form };
        onChange(obj);
      }
    };
    const handleSearch = (e, form) => {
      if (onSearch) {
        onSearch(e, form);
      }
    };
    const handleTreeExpand = (expandedKeys) => {
      if (onExpand) {
        onExpand(expandedKeys);
      }
    };
    const handleTreeCheck = (key) => {
      if (onCheck) {
        onCheck(key, form);
      }
    };
    const handleTreeSelect = (key, info) => {
      if (onSelect) {
        onSelect(key, info);
      }
    };
    const handleCalSelect = (e, { source }, form) => {
      if (onSelect) {
        onSelect(e, { source }, form);
      }
    };

    // 监听表单数据
    const handleValuesChange = (
      changeValue: object,
      allValues: object,
    ): void => {
      onChange?.(changeValue, allValues);
    };

    return (
      <div
        className={`p-r p-t-s flex-1 flex-col flex-justify-between ${className}`}
      >
        <Skeleton loading={loading}>
          <Form
            ref={formRef}
            form={form}
            {...formLayout}
            colon={false}
            initialValues={initialData}
            labelWrap
            onValuesChange={_.debounce(handleValuesChange)}
          >
            {config.map((item, index) => renderFormItem(item, index))}
          </Form>
        </Skeleton>
        {!canEdit && showMaintenance ? (
          <FormMaintenance data={initialData} />
        ) : null}
      </div>
    );
  },
);

export default FormTemplate;
