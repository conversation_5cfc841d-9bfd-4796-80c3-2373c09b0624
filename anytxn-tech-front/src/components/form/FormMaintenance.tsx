import useIntlCustom from '@/common/hooks/useIntlCustom';
import { RENDER_TYPE } from '@/constants/publicConstant';
import { renderValueByType } from '@/utils/comUtil';
import { Col, Row } from 'antd';
import React, { ReactNode } from 'react';
import styles from './form.less';
import { IFormMaintenance } from './types/IFormMaintenance';

const FormMaintenance: React.FC<IFormMaintenance> = ({
  data,
  showVersion = false,
  showUserInfo = true,
}) => {
  const { version, createTime, updateTime, createUser, updateUser } =
    data || {};
    
  let child: any = {};
  if (showVersion) {
    child = { version };
  }
  if (showUserInfo) {
    child = {
      ...child,
      createUser,
      createTime: renderValueByType(createTime, {
        valueType: RENDER_TYPE.DateTime,
      }),
      updateTime: renderValueByType(updateTime, {
        valueType: RENDER_TYPE.DateTime,
      }),
      updateUser,
    };
  }
  const { formateHtmlText } = useIntlCustom();
  const renderChild = () => {
    const res: ReactNode[] = [];
    for (const key in child) {
      let span = key === 'version' ? 3 : 5;
      let className = key === 'version' ? 'error-color' : '';
      res.push(
        <Col key={key} span={span} className="m-r-s">
          <span className={className}>
            {formateHtmlText('common', key)} : {child[key]}
          </span>
        </Col>,
      );
    }
    return res;
  };

  return (
    <Row className={`flex-row p-tb-s ${styles.formMaintenance}`}>
      {renderChild()}
    </Row>
  );
};

export default FormMaintenance;
