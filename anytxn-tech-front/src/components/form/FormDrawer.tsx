import React from 'react';
import { RedoOutlined, SaveOutlined } from '@ant-design/icons';
import { Drawer, Button } from 'antd';
import _ from 'lodash';
import useIntlCustom from '@/common/hooks/useIntlCustom';
import { OPERATE_TYPE } from '@/constants/publicConstant';

interface IFormDrawerProps {
  children?: React.ReactNode;
  title?: React.ReactNode | string;
  extra?: React.ReactNode | string; // 右侧扩展组件，优先级高于 showAction
  type?: string;
  width: string;
  open: boolean;
  destroyOnClose?: boolean;
  showAction?: boolean;
  resetTitle?: string;
  submitTitle?: string;
  submitLoading?: boolean;
  intlPrefix?: string;
  onBack?: () => void;
  onReset?: () => void;
  onSubmit?: () => void;
}
const FormDrawer: React.FC<IFormDrawerProps> = ({
  children,
  extra,
  title = '模块名称',
  type = '', // OPERATE_TYPE
  width = '70%',
  open = false,
  destroyOnClose = true,
  submitLoading = false,
  intlPrefix = '',
  showAction = true,
  resetTitle = '重置',
  submitTitle = '提交',
  onBack = () => {},
  onReset = () => {},
  onSubmit = () => {},
}) => {
  const { formatActionTitle } = useIntlCustom();
  const getActionNode = () => {
    const disabled = type === OPERATE_TYPE.detail;
    return disabled ? null : (
      <>
        <Button icon={<RedoOutlined />} style={{ marginRight: 8 }} onClick={onReset}>
          {resetTitle}
        </Button>
        <Button
          type="primary"
          icon={<SaveOutlined />}
          loading={submitLoading}
          onClick={_.debounce(onSubmit)}
        >
          {submitTitle}
        </Button>
      </>
    );
  };

  return (
    <Drawer
      title={typeof title === 'string' ? formatActionTitle(type, intlPrefix, title) : title}
      placement="right"
      width={width}
      open={open}
      maskClosable={false}
      destroyOnClose={destroyOnClose}
      extra={extra || (showAction ? getActionNode() : null)}
      // footer={showAction ? this.getActionNode() : null}
      onClose={onBack}
    >
      {children || <div />}
    </Drawer>
  );
};
export default FormDrawer;
