import { IFormActionPermissionObj } from '@/types/ICommon';

export interface IFormActionProps {
  title?: React.ReactNode | string;
  extra?: React.ReactNode;
  showEdit?: boolean;
  showSubmit: boolean;
  showCreate: boolean;
  showCancel?: boolean;
  showDelete?: boolean;
  submitLoading?: boolean;
  permissionObj?: IFormActionPermissionObj;
  onEdit?: () => void;
  onSubmit?: () => void;
  onCreate?: () => void;
  onCancel?: () => void;
  onDelete?: () => void;
}
