import { IFormConfig } from '@/types/IForm';
import { MutableRefObject } from 'react';

export type PropsType = {
  disabled: boolean;
  style: object;
  width?: number;
  [key: string]: unknown;
};

export interface IFormTemplateProps {
  ref?: any;
  formRef?: MutableRefObject<any> | undefined;
  config: Array<IFormConfig>;
  loading?: boolean;
  canEdit: boolean;
  showMaintenance: boolean;
  className?: string;
  formLayout?: object;
  initialData: any;
  intlPrefix: string;
  editTableRefList?: any;
  onChange?: (obj: object, allValues?: object) => void;
  onExpand?: (key: string) => void;
  onCheck?: (key: string, form: object) => void;
  onLoad?: (form: object) => void;
  onSelect?: (key: string, info: object, form?: object) => void;
  onSearch?: (key: string, form: object) => void;
}
export interface IformData {
  data?: object;
  type?: string;
  title?: string;
  prefix?: string;
  format?: string;
  name?: string;
  label?: string;
  length?: number;
  rules?: object;
  formlayoutType: string;
}
