import useIntlCustom from '@/common/hooks/useIntlCustom';
import { renderValueByType } from '@/utils/comUtil';
import { Descriptions } from 'antd';
import _ from 'lodash';
import React from 'react';

interface IComDescriptionProps {
  descriptionsItems: any;
  prefix: string; // 国际化key
  num?: number;
}
const TabDescription: React.FC<IComDescriptionProps> = ({
  descriptionsItems,
  prefix,
  num,
}) => {
  const { translate } = useIntlCustom();
  const formatDescriptionsItems = () => {
    let arr: Array<any> = [];
    for (let i in descriptionsItems) {
      const itemType = typeof descriptionsItems[i];
      const item = descriptionsItems[i];
      if (itemType === 'object') {
        let childrenVal: any = '';
        // 判断prefixKey属性是否存在，并做数据处理
        let itemPrefix = _.isNil(item.prefixKey) ? prefix : item.prefixKey;
        if (!_.isNil(item.context)) {
          childrenVal = renderValueByType(item.context, {
            ...item,
            prefix: itemPrefix,
          });
        }
        const labelVal = translate(itemPrefix, i);
        arr.push({
          key: i,
          label: labelVal,
          children: childrenVal,
        });
      } else {
        arr.push({
          key: i,
          label: translate(prefix, i),
          children: descriptionsItems[i],
        });
      }
    }
    return arr;
  };
  return (
    <Descriptions
      bordered
      column={num || 3}
      items={formatDescriptionsItems()}
      style={{ marginBottom: '24px' }}
    />
  );
};
export default TabDescription;
