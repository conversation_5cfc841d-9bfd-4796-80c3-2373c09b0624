import useIntlCustom from '@/common/hooks/useIntlCustom';
import { I18N_COMON_PAGENAME, REQUEST_TYPE } from '@/constants/publicConstant';
import {
  getActionColumnWidth,
  renderColumnByType,
  showTotal,
} from '@/utils/comUtil';
import { Table } from 'antd';
import _ from 'lodash';
import React, { FC, useEffect, useState } from 'react';
import styles from './table.less';
import { ICommonTableProps } from './type/ICommonTable';

const PaginationCom = React.lazy(() => import('./Pagination'));

// 默认分页数据
const defaultPagination = { currentPage: 1, defaultPageSize: 50, pageSize: 50 };

const CommonTable: FC<ICommonTableProps> = ({
  rowKey = 'id',
  columns = [],
  optionList = [],
  paginationConfig = { ...defaultPagination },
  dataSource = [],
  loading = false,
  intlPrefix,
  components,
  serviceType = 'param',
  props = {},
  onAction = () => {},
  onChange = () => {},
  getOptions = () => {},
}) => {
  const { translate, renderActionColumn } = useIntlCustom();
  const [tableData, setTableData] = useState<any[]>([]);

  useEffect(() => {
    Array.isArray(dataSource) ? setTableData(dataSource) : setTableData([]);
  }, [dataSource]);
  // 处理数据
  const setColumns = (columns: any) => {
    const result = columns.map((column: any) => {
      column = { width: 120, align: 'center', ...column };
      let { title, key, prefix, children } = column;
      // 可以每列单独传入prefix，否则用公共的intlPrefix
      column.prefix = prefix || intlPrefix;
      title = key === 'option' ? title : translate(prefix || intlPrefix, title);
      // 根据valueType格式化数据
      const tempColumn = renderColumnByType(column);
      delete tempColumn.valueType;
      if (Array.isArray(children)) {
        tempColumn.children = setColumns(children);
      }
      return { ...tempColumn, title };
    });

    const opreateList: any = (row: any) =>
      !_.isEmpty(getOptions(row)) ? getOptions(row) : optionList;

    const optionColumn = [
      {
        title: translate(I18N_COMON_PAGENAME.COMMON, 'option'),
        dataIndex: 'option',
        key: 'option',
        align: 'center',
        fixed: 'right',
        width: getActionColumnWidth(opreateList()?.length),
        render: (_: any, row: any) =>
          renderActionColumn(opreateList(row), (type: string) =>
            onAction(type, row),
          ),
      },
    ];

    return [...result, ...optionColumn];
  };

  const pagination =
    typeof paginationConfig === 'object'
      ? { ...defaultPagination, ...paginationConfig, showTotal }
      : paginationConfig;
  const col: Array<any> = setColumns(columns);

  // 参数前端自己实现分页，走以下逻辑
  const paginationComNode: any = {
    param: (
      <PaginationCom
        tableData={tableData}
        handleTableChange={(val) => onChange?.(val)}
      />
    ),
    business: <></>,
    mock: <></>,
  };

  return (
    <>
      <Table
        className={
          serviceType === REQUEST_TYPE.param ? styles.commonParamTable : ''
        }
        tableLayout="fixed"
        rowKey={rowKey}
        components={components}
        dataSource={tableData}
        columns={col}
        loading={loading}
        // serviceType 有值说明不需要前端自己做分页，没值值需要前端实现分页功能
        // @ts-ignore
        pagination={serviceType === REQUEST_TYPE.param ? false : pagination}
        scroll={{
          x: 'max-content',
          y: 2000,
        }}
        onChange={onChange}
        {...props}
      />
      {paginationComNode[serviceType]}
    </>
  );
};

export default CommonTable;
