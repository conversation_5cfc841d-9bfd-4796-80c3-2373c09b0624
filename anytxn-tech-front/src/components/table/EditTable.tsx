// 可编辑表格组件
import { FC, useRef, useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { Form } from 'antd';
import { EditableProTable } from '@ant-design/pro-components';
import type { EditableFormInstance, ActionType } from '@ant-design/pro-components';
import { FORM_ERROR_CODE, I18N_COMON_PAGENAME, OPERATE_TYPE } from '@/constants/publicConstant';
import useIntlCustom from '@/common/hooks/useIntlCustom';
import { IEditTableProps } from './type/IEditTable';
import useEditTable from './hooks/useEditTable';

// 新增按钮样式
const createButtonStyle = {
  position: 'absolute',
  right: 0,
  top: '-3.5rem',
  width: 'max-content',
  display: 'flex',
};

const EditTable: FC<IEditTableProps> = forwardRef(
  (
    {
      rowKey,
      columns,
      dataSource,
      intlPrefix,
      canEdit,
      editableType = 'single',
      showCreate = true,
      optionCount = 0,
      loading = false,
      orderFiled,
      getOptionList = () => [],
      afterMount = () => {},
      onCreate = (index: number) => ({ id: index + 1 }),
      onAction = () => {},
    },
    ref = null,
  ) => {
    // hooks变量
    const { translate, renderButton } = useIntlCustom();
    const [editableKeys, setEditableKeys] = useState<string[]>([]);
    const [tableData, setTableData] = useState<Array<any>>([]);
    const editableFormRef = useRef<EditableFormInstance>();
    const actionRef = useRef<ActionType>(null);
    const [form] = Form.useForm();
    const { getColumns } = useEditTable({
      intlPrefix,
      rowKey,
      optionCount,
      orderFiled,
      editableKeys,
      tableData,
      setTableData,
      getOptionList,
      onAction,
    });
    // 副作用
    // 暴露给父组件调用的方法
    useImperativeHandle(ref, () => {
      return {
        // 校验表单
        async validateFields() {
          try {
            // 判断是否正在编辑
            if (editableKeys.length) {
              return { errorCode: FORM_ERROR_CODE.EDITING };
            }
            await editableFormRef?.current?.validateFields();
            return tableData;
          } catch (error) {
            return null;
          }
        },
        // 重置
        resetFields() {
          if (Array.isArray(editableKeys)) {
            setEditableKeys([]);
            form.resetFields();
          }
          setTableData(dataSource);
        },
        // 获取当前表格数据
        getFieldsValue() {
          return editableFormRef?.current?.getFieldsValue();
        },
        // 手动设置行数据
        setRowData(rowIndex: number, data: any) {
          editableFormRef.current?.setRowData?.(rowIndex, { ...data });
        },
        // 手动设置可编辑的行
        setCustomEditableKeys(editTableKeys: any) {
          setEditableKeys(editTableKeys);
        },
        // 获取正在编辑的行
        getEditableKeys() {
          return editableKeys;
        },
      };
    });
    useEffect(() => {
      setTableData(Array.isArray(dataSource) ? dataSource : []);
    }, [dataSource]);
    useEffect(() => {
      afterMount(form);
    }, []);

    const props: any = {
      rowKey,
      loading,
      // 列表配置
      columns: getColumns(columns),
      // 数据源
      value: tableData,
      // 新增按钮配置
      recordCreatorProps:
        canEdit && showCreate
          ? {
              position: 'bottom',
              // 每次新增的时候需要rowKey
              record: onCreate,
              creatorButtonText: translate(I18N_COMON_PAGENAME.COMMON, 'create'),
              style: createButtonStyle,
            }
          : false,
      // 可编辑表格实例
      editableFormRef,
      // action的实例
      actionRef,
      // 可编辑表格配置
      editable: {
        form,
        // 单行编辑还是多行编辑
        type: editableType,
        // 编辑中的行keys
        editableKeys,
        onChange: setEditableKeys,
        // 默认操作列的配置
        actionRender: (row: any, config: any, defaultDom: any) => [defaultDom.save, defaultDom.cancel],
        saveText: renderButton({ type: OPERATE_TYPE.save }),
        cancelText: renderButton({ type: OPERATE_TYPE.cancel }),
      },
      onChange: setTableData,
    };

    return <EditableProTable {...props} />;
  },
);

export default EditTable;
