import { TablePaginationConfig } from 'antd';
import { IColumns } from '@/types/ICommon';
import { TAction } from '@/types/TCommon';

/**
 * CommonTable的props约束接口
 */
export interface ICommonTableProps {
  /**
   * 每行的唯一索引
   */
  rowKey?: string;
  /**
   * 请求接口的类型
   */
  serviceType?: string;
  /**
   * 列表配置
   */
  columns: Array<IColumns>;
  /**
   * 操作列配置
   */
  optionList?: Array<string | TAction>;
  /**
   * 分页配置
   */
  paginationConfig: TablePaginationConfig | false;
  /**
   * 表格数据源
   */
  dataSource: Array<any>;
  /**
   * 表格是否正在加载
   */
  loading?: boolean;
  /**
   * 国际化模块前缀
   */
  intlPrefix: string;
  /**
   * 覆盖默认的 table 元素
   */
  components?: any;
  /**
   * 其余传递给Table的属性
   */
  props?: object;
  /**
   * 操作列回调
   */
  onAction?: (type: string, row: object) => void;
  /**
   * 表格改变事件
   */
  onChange?: (pagination: any) => void;
  /**
   * 表格改变事件
   */
  getOptions?: (row?: object) => void;
}
