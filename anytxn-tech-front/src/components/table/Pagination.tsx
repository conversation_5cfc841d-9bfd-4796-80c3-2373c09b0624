import { Button, <PERSON> } from 'antd';
import React, { useState } from 'react';
import cls from 'classnames';
import _ from 'lodash';
import DICT_CONSTANTS from '@/constants/dictConstants';
import { DEFAULT_PAGINATION, I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import useIntlCustom from '@/common/hooks/useIntlCustom';
import { IEnumsData } from '@/types/ICommon';
import styles from './table.less';

interface IPaginationProps {
  tableData: Array<[]>;
  handleTableChange?: (pageNo: number) => void;
}

interface IPageData {
  current: number;
  pageSize: number;
}

const Pagination: React.FC<IPaginationProps> = ({ tableData, handleTableChange = () => {} }) => {
  const { translate } = useIntlCustom();
  const [pageData, setPagaData] = useState<IPageData>({
    current: DEFAULT_PAGINATION.currentPage,
    pageSize: DEFAULT_PAGINATION.pageSize,
  });

  const handleClick = (pagination: any) => {
    setPagaData(pagination);
    handleTableChange(pagination);
  };

  // 页码下拉枚举
  const pageOptions = DICT_CONSTANTS.PAGEOPTIONS.map((item: IEnumsData) => ({
    label: translate(I18N_COMON_PAGENAME.COMMON, item.label ?? ''),
    value: item.value,
  }));

  // 上一页按钮
  const previousNode = () => {
    if (pageData.current === 1) return;
    return (
      <Button
        className="pageBtn"
        type="text"
        onClick={() => handleClick({ ...pageData, current: pageData.current - 1 })}
      >
        {translate(I18N_COMON_PAGENAME.COMMON, 'previousPage')}
      </Button>
    );
  };

  // 下一页按钮
  const nextNode = () => {
    if (_.isEmpty(tableData) || tableData.length < pageData.pageSize) return;
    return (
      <Button
        className="pageBtn"
        type="text"
        onClick={() => handleClick({ ...pageData, current: pageData.current + 1 })}
      >
        {translate(I18N_COMON_PAGENAME.COMMON, 'nextPage')}
      </Button>
    );
  };

  return (
    <div className={cls('flex-row', styles.paginationContent)}>
      {previousNode()}
      {nextNode()}
      <Select
        defaultValue={pageOptions[2]?.label}
        style={{ width: 100 }}
        options={pageOptions}
        onChange={(val) => handleClick({ ...pageData, pageSize: Number(val) })}
      />
    </div>
  );
};
export default React.memo(Pagination);
