// pageTemplate搜索框组件
import { useState } from 'react';
import { Search } from '@/components';
import { IUsePageSearch } from '../types/IPageTemplate';

const usePageSearch = ({ searchConfig, intlPrefix, pagination, getTableData }: IUsePageSearch) => {
  const [searchValue, setSearchValue] = useState<object>({ ...(searchConfig.resetValue || {}) });
  const [searchBtnDisabled, setSearchBtnDisabled] = useState<boolean>(false);

  // 搜索事件
  const handleSearch = (searchValue: object): void => {
    setSearchValue(searchValue);
    getTableData({ ...pagination }, searchValue);
  };
  // 渲染搜索框
  const renderSearch = () => {
    const { searchSource, searchValue, resetValue, props = {} } = searchConfig;
    return (
      <Search
        searchValue={searchValue}
        resetValue={resetValue}
        searchSource={searchSource}
        intlPrefix={intlPrefix}
        buttonDisabled={searchBtnDisabled}
        onSearch={handleSearch}
        {...props}
      />
    );
  };

  return {
    searchValue,
    setSearchBtnDisabled,
    renderSearch,
  };
};

export default usePageSearch;
