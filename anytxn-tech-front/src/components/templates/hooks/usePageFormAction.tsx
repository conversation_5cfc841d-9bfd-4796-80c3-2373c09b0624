// pageTemplate表格/表单上方操作栏
import useIntlCustom from '@/common/hooks/useIntlCustom';
import { FormAction } from '@/components';
import {
  COMPONENT_TYPE,
  FORM_ERROR_CODE,
  I18N_COMON_PAGENAME,
  NOTIFICATION_TYPE,
  OPERATE_TYPE,
} from '@/constants/publicConstant';
import { ReactElement, useState } from 'react';
import usePageUrl from './usePageUrl';

const usePageFormAction = ({
  formRef,
  type,
  rowData,
  searchValue,
  pagination,
  childrenType,
  urlObj,
  formConfig,
  formActionConfig,
  getTableData,
  handleAction,
  handleCardBack,
}: any) => {
  const [submitLoading, setSubmitLoading] = useState(false);
  const { openNotificationTip } = useIntlCustom();
  const { getRequestFunc } = usePageUrl({ urlObj });

  // 新增-编辑数据
  const editData = async (data: any): Promise<void> => {
    try {
      const { getRequestData } = urlObj;
      const editFunc = getRequestFunc(OPERATE_TYPE[type] || type);
      // 自定义传参（根据表单数据，操作类型，查询的数据做处理）
      const getPostData = getRequestData
        ? getRequestData(data, type, rowData)
        : data;
      setSubmitLoading(true);

      const result = await editFunc({
        interFaceurl: urlObj[type],
        type,
        ...getPostData,
      });
      // 响应成功
      if (result || result?.header) {
        await getTableData(pagination, searchValue);
        handleCardBack();
        openNotificationTip(
          I18N_COMON_PAGENAME.COMMON,
          NOTIFICATION_TYPE.SUCCESS,
          'operationSuccess',
          1,
        );
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSubmitLoading(false);
    }
  };
  // 事件处理
  // 提交表单事件
  const handleCardSubmit = async (): Promise<void> => {
    try {
      const data = await formRef?.current?.onSubmit();
      // 判断有无返回数据或有无返回错误码
      if (!data || data.errorCode) {
        openNotificationTip(
          I18N_COMON_PAGENAME.COMMON,
          NOTIFICATION_TYPE.ERROR,
          data?.errorCode || FORM_ERROR_CODE.CHECK_MSG,
        );
      } else {
        editData({
          formData: {
            // @ts-ignore
            incId: formConfig?.data?.incId,
            ...data.formData,
          },
        });
      }
    } catch (error) {
      console.log('error=', error);
    }
  };
  // 新增事件
  const handleCreate = (): void => {
    handleAction(OPERATE_TYPE.create, {});
  };

  // 渲染组件
  const renderFormAction = (): ReactElement | undefined => {
    const { isShowCardExtra = true } = formConfig?.props || {};
    // 是否展示formAction组件
    if (type === OPERATE_TYPE.detail || !isShowCardExtra) {
      return;
    }
    const {
      showSubmit = true,
      showCreate = true,
      detailShowCreate,
      permissionObj,
      onDetailCreate,
    } = formActionConfig || {};
    // 可调用传进的新增事件
    const onCreate =
      childrenType === COMPONENT_TYPE.TABLE ? handleCreate : onDetailCreate;
    // 根据下方组件类型渲染formAction组件
    return childrenType === COMPONENT_TYPE.TABLE ? (
      <FormAction
        key={childrenType}
        showSubmit={false}
        showCreate={showCreate}
        permissionObj={permissionObj}
        onCreate={onCreate}
      />
    ) : (
      <FormAction
        key={childrenType}
        showSubmit={showSubmit}
        showCreate={detailShowCreate || false}
        submitLoading={submitLoading}
        permissionObj={permissionObj}
        onSubmit={handleCardSubmit}
        onCreate={onCreate}
      />
    );
  };
  return { renderFormAction };
};

export default usePageFormAction;
