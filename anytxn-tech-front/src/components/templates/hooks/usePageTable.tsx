import useIntlCustom from '@/common/hooks/useIntlCustom';
import { CommonTable } from '@/components';
import {
  COMPONENT_TYPE,
  I18N_COMON_PAGENAME,
  NOTIFICATION_TYPE,
  OPERATE_TYPE,
  REQUEST_TYPE,
} from '@/constants/publicConstant';
import { IUsePageTable } from '../types/IPageTemplate';

const usePageTable = ({
  setType,
  setChildrenType,
  setCardShowBack,
  setSearchBtnDisabled,
  setPagination,
  tableConfig,
  urlObj,
  pagination,
  paginationConfig,
  tableData,
  tableLoading,
  intlPrefix,
  getTableData,
  getRequestFunc,
  onAction,
}: IUsePageTable) => {
  const { openNotificationTip } = useIntlCustom();
  // 详情额外数据
  let detailData: Array<string> = [];

  // 删除
  const deleteData = async (data: any, type: string) => {
    try {
      const { getRequestData } = urlObj;
      const deleteFunc = getRequestFunc(OPERATE_TYPE[type]);
      // 自定义传参
      const getPostData = getRequestData ? getRequestData(data, type) : data;
      const result = await deleteFunc({
        interFaceurl: urlObj.delete,
        ...getPostData,
      });
      if (result) {
        getTableData(pagination);
        openNotificationTip(
          I18N_COMON_PAGENAME.COMMON,
          NOTIFICATION_TYPE.SUCCESS,
          'deleteSuccess',
          1,
        );
      } else {
        openNotificationTip(
          I18N_COMON_PAGENAME.COMMON,
          NOTIFICATION_TYPE.ERROR,
          'deleteFailed',
        );
      }
    } catch (error) {
      console.error(error);
    }
  };
  // 详情（联表场景，需要额外请求其他接口）
  const getDetailData = async (item: any) => {
    try {
      const { getRequestData } = urlObj;
      const detailFunc = getRequestFunc(OPERATE_TYPE.detail);
      const params = {
        interFaceurl: urlObj?.detail,
        searchValue: { checkItemPortfNo: item.checkItemPortfNo },
      };
      const getPostData = getRequestData
        ? getRequestData({ ...params }, OPERATE_TYPE.detail)
        : params;
      if (!detailFunc) {
        return;
      }
      const res = await detailFunc({
        ...getPostData,
        pagination,
      });
      const { data } = res;
      if (data) {
        detailData = data;
      }
    } catch (error) {
      // 打印错误日志
      console.log(error);
      // 获取详情失败提示
      openNotificationTip('common', NOTIFICATION_TYPE.ERROR, 'detailFailed');
    }
  };
  // 操作事件
  const handleAction = async (actionType: any, row: any): Promise<void> => {
    let type = actionType;
    // flag为true时，不再下钻
    let flag = false;
    let res = row;
    // 优先处理actionType为对象场景
    if (typeof actionType === 'object') {
      type = actionType.type;
      flag = actionType.flag === undefined ? false : actionType.flag;
    }
    // 处理新增、详情、编辑或者复制页面需要额外请求接口获取数据场景的数据封装
    const detailObj = urlObj.detail;
    if (
      typeof detailObj === 'object' &&
      detailObj.URL &&
      detailObj.URL.length > 0
    ) {
      await getDetailData(row);
      res = { ...row, detailList: detailData };
    }
    onAction(type, res);
    // 删除
    if (type === OPERATE_TYPE.delete) {
      deleteData(res, 'delete');
      return;
    } else {
      if (flag) {
        return;
      }
    }
    setType(type);
    setChildrenType(COMPONENT_TYPE.FORM);
    setCardShowBack(true);
    setSearchBtnDisabled(true);
  };

  const handleTableChange = (pagination: any) => {
    const { current: currentPage, pageSize } = pagination;
    setPagination({ currentPage, pageSize });
    getTableData({ currentPage, pageSize });
  };

  // 渲染表格
  const renderTable = () => {
    const {
      rowKey = 'id',
      columns = [],
      optionList = [
        OPERATE_TYPE.detail,
        OPERATE_TYPE.edit,
        OPERATE_TYPE.delete,
      ],
      getOptions,
      props,
    } = tableConfig || {};
    const { serviceType = REQUEST_TYPE.param } = urlObj;

    return (
      <CommonTable
        rowKey={rowKey}
        serviceType={serviceType}
        columns={columns}
        optionList={optionList}
        paginationConfig={paginationConfig === false ? paginationConfig : { ...paginationConfig, ...pagination }}
        dataSource={tableData}
        loading={tableLoading}
        intlPrefix={intlPrefix}
        getOptions={getOptions}
        onAction={handleAction}
        onChange={handleTableChange}
        props={props}
      />
    );
  };
  return {
    renderTable,
    handleAction,
  };
};

export default usePageTable;
