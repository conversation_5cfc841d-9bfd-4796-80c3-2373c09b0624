import { ISearchSource, IUrlObj } from '@/types/ICommon';

/**
 * 搜索框约束接口
 */
export interface ISearchConfig {
  /**
   * search表格配置
   */
  searchSource: Array<ISearchSource>;
  /**
   * search表格查询条件字段
   */
  searchValue: object;
  /**
   * search表格重置查询条件字段
   */
  resetValue: object;
  /**
   * 查询接口默认传参
   */
  defaultParam?: object;
  /**
   * 其余传递给search的props
   */
  props?: object;
}

/**
 * 详情表单配置
 */
export interface IFormConfig {
  /**
   * 表单配置
   */
  config: Array<any>;
  /**
   * 表单数据
   */
  data: object;
  /**
   * 国际化前缀
   */
  intlPrefix: string;
  /**
   * 表单底部是否显示维护信息
   */
  showMaintenance?: boolean;
  /**
   * 是否用自定义组件
   */
  isCustom?: boolean;
  /**
   * 自定义详情组件
   */
  customChildren?: React.ReactNode;
  props?: {
    isShowCardExtra: boolean; // 受否展示额外按钮和面包屑
  };
  /**
   * 额外参数，需要数据处理但不传进表单的参数
   */
  extraProp?: any;
  /**
   * 表单change事件回调
   */
  onChange?: (e) => void;
}

/**
 * 自定义form组件
 */
export interface ICustomFormConfig {
  /**
   * 是否自定义
   */
  isCustom: boolean;
  /**
   * 自定义组件
   */
  customChildren: React.ReactNode;
}

/**
 * 表格上方操作栏约束接口
 */
export interface IFormActionConfig {
  /**
   * 是否展示重置按钮
   */
  showReset?: boolean;
  /**
   * 是否展示提交按钮
   */
  showSubmit?: boolean;
  /**
   * 是否展示新增按钮
   */
  showCreate?: boolean;
  /**
   * 详情是否展示详情
   */
  detailShowCreate?: boolean;
  /**
   * 详情自定义新增方法
   */
  onDetailCreate?: () => void;
}

/**
 * DoubleFormPageTemplate组件props约束接口
 */
export interface IDoubleFormPageTemplateProps {
  /**
   * search组件属性
   */
  searchConfig: ISearchConfig;
  /**
   * 外层form组件属性
   */
  outerFormConfig: IFormConfig | ICustomFormConfig;
  /**
   * form组件属性
   */
  formConfig?: IFormConfig | ICustomFormConfig;
  /**
   * formAction组件属性
   */
  formActionConfig?: IFormActionConfig;
  /**
   * table类型
   */
  tableType?: string; // commom/tree
  /**
   * 请求接口配置
   */
  urlObj: IUrlObj;
  /**
   * 下方组件标题
   */
  cardTitle?: React.ReactNode | string;
  /**
   * 国际化前缀
   */
  intlPrefix: string;
  /**
   * 页面需要查询的字典或参数
   */
  dictEnum?: any;
  /**
   * 操作列事件回调
   */
  onAction?: (type: string, row: object) => void;
}

/**
 * UseDoublePageUrl的约束接口
 */
export interface IUseDoublePageUrl {
  /**
   * search组件属性
   */
  searchConfig?: ISearchConfig;
  /**
   * 请求接口配置
   */
  urlObj: IUrlObj;
}

/**
 * useDoublePageSearch的约束接口
 */
export interface IUseDoublePageSearch {
  /**
   * search组件属性
   */
  searchConfig: ISearchConfig;
  /**
   * 国际化前缀
   */
  intlPrefix: string;
  /**
   * 查询事件
   */
  getData: (searchData: object) => void;
}

/**
 * useDoublePageForm的约束接口
 */
export interface IUseDoublePageForm {
  /**
   * form组件实例
   */
  outerFormRef: any;
  /**
   * form组件属性
   */
  outerFormConfig?: IFormConfig | ICustomFormConfig;
  /**
   * 操作类型
   */
  type: string;
  /**
   * form data
   */
  outerFormData?: object;
}
