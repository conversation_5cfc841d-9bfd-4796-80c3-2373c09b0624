import { IColumns, IFormActionPermissionObj, IPaniation, ISearchSource, IUrlObj } from '@/types/ICommon';
import { TAction } from '@/types/TCommon';
import { TablePaginationConfig } from 'antd';

/**
 * 搜索框约束接口
 */
export interface ISearchConfig {
  /**
   * search表格配置
   */
  searchSource: Array<ISearchSource>;
  /**
   * search表格查询条件字段
   */
  searchValue: object;
  /**
   * search表格重置查询条件字段
   */
  resetValue?: object;
  /**
   * 查询接口默认传参
   */
  defaultParam?: object;
  /**
   * 其余传递给search的props
   */
  props?: object;
}

/**
 * 表格约束接口
 */
export interface ITableConfig {
  /**
   * 表格每行的唯一键
   */
  rowKey?: string;
  /**
   * 表格列表配置
   */
  columns: Array<IColumns>;
  /**
   * 操作列
   */
  optionList?: Array<string | TAction>;
  /**
   * 是否展示分页
   */
  showPagination?: boolean;
  /**
   * 其余传递给table的props
   */
  props?: object;
  /**
   * 表格change事件回调
   */
  onChange?: (record: object | undefined) => void;
  /**
   * 表格操作列
   */
  getOptions?: (row: object | undefined) => void;
}

/**
 * 详情表单配置
 */
export interface IFormConfig {
  /**
   * 表单配置
   */
  config: any;
  /**
   * 表单数据
   */
  data: object;
  /**
   * 国际化前缀
   */
  intlPrefix: string;
  /**
   * 表单底部是否显示维护信息
   */
  showMaintenance?: boolean;
  /**
   * 是否用自定义组件
   */
  isCustom?: boolean;
  /**
   * 自定义详情组件
   */
  customChildren?: React.ReactNode;
  props?: {
    isShowCardExtra?: boolean; // 受否展示额外按钮和面包屑
    editTableRefList?: object;
  };
  /**
   * 表单change事件回调
   */
  onChange?: (e: any) => void;
}

/**
 * 自定义form组件
 */
export interface ICustomFormConfig {
  /**
   * 是否自定义
   */
  isCustom: boolean;
  /**
   * 自定义组件
   */
  customChildren: React.ReactNode;
}

/**
 * 表格上方操作栏约束接口
 */
export interface IFormActionConfig {
  /**
   * 是否展示重置按钮
   */
  showReset?: boolean;
  /**
   * 是否展示提交按钮
   */
  showSubmit?: boolean;
  /**
   * 是否展示新增按钮
   */
  showCreate?: boolean;
  /**
   * 详情是否展示详情
   */
  detailShowCreate?: boolean;
  /**
   * 详情自定义新增方法
   */
  onDetailCreate?: () => void;
  /**
   * 按钮权限
   */
  permissionObj?: IFormActionPermissionObj;
}

/**
 * pageTemplate组件props约束接口
 */
export interface IPageTemplateProps {
  /**
   * search组件属性
   */
  searchConfig: ISearchConfig;
  /**
   * table组件属性
   */
  tableConfig: ITableConfig;
  /**
   * form组件属性
   */
  formConfig?: IFormConfig | ICustomFormConfig;
  /**
   * formAction组件属性
   */
  formActionConfig?: IFormActionConfig;
  /**
   * table类型
   */
  tableType?: string; // commom/tree
  /**
   * 请求接口配置
   */
  urlObj?: IUrlObj;
  /**
   * 下方组件标题
   */
  cardTitle?: React.ReactNode | string;
  /**
   * 国际化前缀
   */
  intlPrefix: string;
  /**
   * 页面需要查询的字典或参数
   */
  dictEnum?: any;
  /**
   * 操作列事件回调
   */
  onAction?: (type: string, row: object) => void;
}

/**
 * usePageForm的约束接口
 */
export interface IUsePageForm {
  /**
   * form组件实例
   */
  formRef: any;
  /**
   * form组件属性
   */
  formConfig?: IFormConfig | ICustomFormConfig;
  /**
   * 操作类型
   */
  type: string;
}

/**
 * usePageFormAction的约束接口
 */
export interface IUsePageFormAction {
  /**
   * form组件实例
   */
  formRef: any;
  /**
   * 操作类型
   */
  type: string;
  /**
   * 子组件类型
   */
  childrenType: string;
  /**
   * 请求接口配置
   */
  urlObj: IUrlObj;
  /**
   * form组件属性
   */
  formConfig?: IFormConfig;
  /**
   * formAction组件属性
   */
  formActionConfig?: IFormActionConfig;
  /**
   * 操作列事件
   */
  handleAction: (type: string, row: any) => void;
  /**
   * 返回事件
   */
  handleCardBack: () => void;
}

/**
 * usePageSearch的约束接口
 */
export interface IUsePageSearch {
  /**
   * search组件属性
   */
  searchConfig: ISearchConfig;
  /**
   * 国际化前缀
   */
  intlPrefix: string;
  /**
   * 分页配置
   */
  pagination: IPaniation;
  /**
   * 查询事件
   */
  getTableData: (pagination: IPaniation, searchData: object) => void;
}

/**
 * usePageTable的约束接口
 */
export interface IUsePageTable {
  setType: any;
  setChildrenType: any;
  setCardShowBack: any;
  setSearchBtnDisabled: any;
  setPagination: any;
  /**
   * table组件属性
   */
  tableConfig?: ITableConfig;
  /**
   * 请求接口配置
   */
  urlObj: IUrlObj;
  /**
   * 分页配置
   */
  pagination: IPaniation;
  /**
   * 分页配置
   */
  paginationConfig: false | TablePaginationConfig;
  /**
   * table是否加载中
   */
  tableLoading: boolean;
  /**
   * 表格数据
   */
  tableData: any[];
  /**
   * 国际化前缀
   */
  intlPrefix: string;
  /**
   * 查询事件
   */
  getTableData: (pagination?: IPaniation, searchData?: object) => void;
  /**
   * 获取请求方法
   */
  getRequestFunc: (type: string) => any;
  /**
   * 操作列回调
   */
  onAction: (type: string, row: object) => void;
}

/**
 * usePageUrl的约束接口
 */
export interface IUsePageUrl {
  /**
   * search组件属性
   */
  searchConfig?: ISearchConfig;
  /**
   * table组件属性
   */
  tableConfig?: ITableConfig;
  /**
   * 请求接口配置
   */
  urlObj: IUrlObj;
}
