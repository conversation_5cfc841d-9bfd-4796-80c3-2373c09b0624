import type { TableColumnsType } from 'antd';
import { TAction } from '@/types/TCommon';
import { IColumns } from '@/types/ICommon';
// 搜索框类型
export type TSearchConfig = {
  searchSource: Array<any>;
  searchValue: object;
  resetValue: object;
  defaultParam?: object;
  props?: object;
};
export type TTableConfig = {
  rowKey?: string;
  columns: Array<IColumns>;
  optionList?: Array<string | TAction>;
  showPagination?: boolean;
  props?: object;
  onChange?: (record: object | undefined) => void;
};
// 树形表格
export type TTreeTableConfig = {
  columns: TableColumnsType;
  optionList?: Array<string>;
  props?: object;
};
/**
 * 详情表单配置
 */
export type TFormConfig = {
  config: Array<any>;
  data: object;
  intlPrefix: string;
  showMaintenance?: boolean;
  /**
   * 是否用自定义组件
   */
  isCustom?: boolean;
  /**
   * 自定义详情组件
   */
  customChildren?: React.ReactNode;
  onChange?: (e: any) => void;
  props?: {
    isShowCardExtra?: boolean; // 受否展示额外按钮和面包屑
    editTableRefList?: object; 
  };
};
/**
 * 表格上方操作栏
 */
export type TFormActionConfig = {
  /**
   * 是否展示重置按钮
   */
  showReset?: boolean;
  /**
   * 是否展示提交按钮
   */
  showSubmit?: boolean;
  /**
   * 是否展示新增按钮
   */
  showCreate?: boolean;
  /**
   * 是否展示修改按钮
   */
  showEdit?: boolean;
  /**
   * 详情是否展示详情
   */
  detailShowCreate?: boolean;
  /**
   * 详情自定义新增方法
   */
  onDetailCreate?: () => void;
};