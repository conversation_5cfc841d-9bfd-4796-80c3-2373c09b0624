import { LOCAL } from '@/constants/publicConstant';
import { themeColor } from '@/constants/styleConstant';
import { Button, ConfigProvider } from 'antd';
import _ from 'lodash';

const theme = localStorage.getItem(LOCAL.THEME) || '';
// const getHoverColors = (colors) =>
//   colors.map((color) => new TinyColor(color).lighten(5).toString());
// const getActiveColors = (colors) =>
//   colors.map((color) => new TinyColor(color).darken(5).toString());
export default ({
  children,
  color = theme || themeColor.green,
  type,
  size,
  icon,
  style,
  disabled = false,
  onClick = () => {},
}: any) => (
  <ConfigProvider
    theme={{
      components: {
        Button: {
          colorPrimary: color,
          // colorPrimary: `linear-gradient(135deg, ${themeGradientButton[color].join(', ')})`,
          // colorPrimaryHover: `linear-gradient(135deg, ${getHoverColors(themeGradientButton[color]).join(', ')})`,
          // colorPrimaryActive: `linear-gradient(135deg, ${getActiveColors(themeGradientButton[color]).join(', ')})`,
          // lineWidth: 0,
        },
      },
    }}
  >
    <Button
      type={type || 'primary'}
      size={size || 'middle'}
      disabled={disabled}
      icon={icon}
      style={style}
      onClick={_.debounce(onClick)}
    >
      {children}
    </Button>
  </ConfigProvider>
);
