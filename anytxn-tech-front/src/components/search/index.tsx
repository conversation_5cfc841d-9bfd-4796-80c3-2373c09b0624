import useIntlCustom from '@/common/hooks/useIntlCustom';
import { COMPONENT_TYPE, LOCAL } from '@/constants/publicConstant';
import styleConstant, { themeColor } from '@/constants/styleConstant';
import { PlusOutlined, RedoOutlined, SearchOutlined } from '@ant-design/icons';
import {
  Button,
  Cascader,
  Checkbox,
  DatePicker,
  Form,
  Input,
  Select,
  TreeSelect,
} from 'antd';
import _ from 'lodash';
import React, { ReactElement, useState } from 'react';
import GradientButton from '../gradientButton';
const defaultWidth = 160;
const mr2rem = { marginRight: '2rem' };
type TSource = {
  value: string;
  type: string;
  width?: number;
  data?: Array<any>; // 数据源，下拉框等组件可以赋值
  ref?: string; // 如果有联动交互，比如B依赖A的值联动，A是主表B是从表，A需要有ref字段识别关联，则B需要有bind=A的绑定字段，同时B的集合需要对bind与A的值做过滤
  // [key: string]: unknown; // 其他动态未知属性
};
interface ISearchProps {
  children?: React.ReactNode;
  searchTitle?: string;
  resetTitle?: string;
  createTitle?: string;
  searchValue: object;
  resetValue: object;
  searchSource: Array<TSource>;
  intlPrefix?: string;
  buttonDisabled?: boolean;
  onSearch: (obj: object) => void;
  onCreate?: () => void;
  onChange?: (changedValues: object, allValues: object) => void;
  onRest?: (searchSource?: object) => void;
}
const theme = localStorage.getItem(LOCAL.THEME) || themeColor.green;
const Search: React.FC<ISearchProps> = ({
  children,
  searchTitle = '',
  resetTitle = '',
  createTitle = '',
  searchValue = { keyword: '', status: 1 },
  resetValue = { keyword: '', status: 1 },
  searchSource = [],
  intlPrefix = '',
  buttonDisabled = false,
  onSearch = () => {},
  onCreate = () => {},
  onChange,
  onRest,
}) => {
  // 内部变量
  const {
    translate,
    getSelectOption,
    defaultInputPlaceholder,
    defaultSelectPlaceholder,
  } = useIntlCustom();
  const [formValue, setFormValue] = useState(searchValue) as any;
  const [form] = Form.useForm();
  // 逻辑处理
  const getItemProp = (item: any) => {
    const itemProp = { rules: item.rules, name: item.value, label: undefined };
    if (item.label) {
      itemProp.label = intlPrefix
        ? translate(intlPrefix, item.label)
        : item.label;
    }
    return itemProp;
  };
  const renderItem = (data: any) => {
    // 遍历剔除不属于组件的属性
    const itemProps: any = { allowClear: true, style: {} };
    for (const p in data) {
      if (
        ![
          'type',
          'value',
          'label',
          'rules',
          'data',
          'showKey',
          'ref',
          'bind',
          'prefix',
        ].includes(p)
      ) {
        itemProps[p] = data[p];
      }
    }
    const { type, width } = data;
    let item: ReactElement | null = null;
    if (type === COMPONENT_TYPE.INPUT) {
      itemProps.style = { width: width || defaultWidth };
      item = <Input placeholder={defaultInputPlaceholder} {...itemProps} />;
    } else if (type === 'Checkbox') {
      item = <Checkbox.Group options={data.data} />;
    } else if (type === COMPONENT_TYPE.SELECT) {
      itemProps.style = { width: width || defaultWidth };
      // 把关联数据和全部选项过滤出来
      const selectSource =
        data.data && data.bind
          ? data.data.filter(
              (t: any) => t.bind === formValue[data.bind] || t.key === '',
            )
          : data.data;
      // 可以使用自定义模块
      const prefix = data.prefix || intlPrefix;
      // 是否需要国际化，默认需要
      let isInt = !(data.isint && data.isint === '0');
      item = (
        <Select
          placeholder={defaultSelectPlaceholder}
          {...itemProps}
          options={
            isInt
              ? getSelectOption(selectSource, data.showKey, prefix)
              : selectSource
          }
        />
      );
    } else if (type === COMPONENT_TYPE.TREE_SELECT) {
      itemProps.style = { width: width || defaultWidth };
      item = <TreeSelect {...itemProps} treeData={data.data} />;
    } else if (type === COMPONENT_TYPE.CASCADER) {
      itemProps.style = { width: width || defaultWidth };
      item = (
        <Cascader
          placeholder={defaultSelectPlaceholder}
          {...itemProps}
          options={data.data}
        />
      );
    } else if (type === COMPONENT_TYPE.DATE_PICKER) {
      itemProps.style = { width: width || defaultWidth };
      item = <DatePicker {...itemProps} />;
    } else if (type === COMPONENT_TYPE.RANGE_PICKER) {
      itemProps.style = { width: width || 240 };
      item = <DatePicker.RangePicker {...itemProps} />;
    } else {
      item = <Input {...itemProps} />;
    }
    return item;
  };
  // 事件处理
  const handleValuesChange = (
    changedValues: object,
    allValues: object,
  ): void => {
    // 传入onChange事件则自行处理
    if (onChange) {
      onChange(changedValues, allValues);
    }
    // 获取联动的字段
    let key = Object.keys(changedValues)[0];
    let ref: string | any = '';
    searchSource.forEach((item) => {
      if (item.value === key && item.ref) {
        ref = item.ref;
      }
    });
    // 如果有联动字段，对应字段置空
    if (ref) {
      // 更新数据
      setFormValue({ ...allValues, [ref]: null });
      // 刷新页面
      form.setFieldsValue({ [ref]: null });
    } else {
      setFormValue(allValues);
    }
  };
  const handleClear = () => {
    // form.resetFields(); // 并没有达到重置效果
    form.setFieldsValue(resetValue); // 通过设置初始值达到重置效果
    if (onRest) {
      onRest(searchValue);
    }
    handleSearch();
  };
  const handleSearch = () => {
    form
      .validateFields()
      .then((values) => {
        onSearch(values);
      })
      .catch((errors) => {
        // 表单验证失败，处理错误
        if (errors.errorFields) {
          // 显示错误提示
          form.setFields(errors.errorFields);
        }
      });
  };

  return (
    <div className="search-box">
      <Form
        layout="inline"
        colon={false}
        form={form}
        initialValues={searchValue}
        onValuesChange={_.debounce(handleValuesChange)}
      >
        {searchSource.map((item) => (
          <Form.Item
            key={item.value}
            {...getItemProp(item)}
            style={{ ...styleConstant.mtbs, ...mr2rem }}
          >
            {renderItem(item)}
          </Form.Item>
        ))}
        <Button
          type="primary"
          icon={<SearchOutlined />}
          disabled={buttonDisabled}
          style={styleConstant.mtbs}
          onClick={_.debounce(() => handleSearch())}
        >
          {searchTitle}
        </Button>
        <Button
          icon={<RedoOutlined />}
          disabled={buttonDisabled}
          style={{ ...styleConstant.mtbs, ...styleConstant.mls, ...mr2rem }}
          onClick={_.debounce(handleClear)}
        >
          {resetTitle}
        </Button>
        {createTitle && (
          <GradientButton
            color={theme}
            type="primary"
            size="middle"
            icon={<PlusOutlined />}
            disabled={buttonDisabled}
            style={{ ...styleConstant.mtbs, ...mr2rem }}
            onClick={onCreate}
          >
            {createTitle}
          </GradientButton>
        )}
        {children && <div style={styleConstant.mtbs}>{children}</div>}
      </Form>
    </div>
  );
};
export default Search;
