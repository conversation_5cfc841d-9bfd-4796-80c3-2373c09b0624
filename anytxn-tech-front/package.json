{"private": true, "author": "shun.cao <<EMAIL>>", "scripts": {"dev": "max dev", "build": "max build", "format": "prettier --cache --write .", "prepare": "husky", "postinstall": "max setup", "setup": "max setup", "start": "npm run dev"}, "dependencies": {"@ant-design/icons": "^5.5.2", "@ant-design/pro-components": "^2.8.2", "@umijs/max": "^4.3.35", "ahooks": "^3.8.4", "antd": "^5.22.4", "dayjs": "^1.11.13", "js-cookie": "^3.0.5", "json-bigint": "^1.0.0", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "umi-request": "^1.4.0"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/json-bigint": "^1.0.4", "@types/lodash": "^4.17.13", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "husky": "^9", "lint-staged": "^13.2.0", "prettier": "^2.8.7", "prettier-plugin-organize-imports": "^3.2.2", "prettier-plugin-packagejson": "^2.4.3", "typescript": "^5.0.3"}}