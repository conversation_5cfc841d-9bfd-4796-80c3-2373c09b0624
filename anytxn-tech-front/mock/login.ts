export default {
  'POST /api/login': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: {
      id: 1,
      userCode: 'liwenhai',
      userName: '李文海',
      mobile: '18617320550',
      email: '<EMAIL>',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      organizationId: '03-01',
      organizationName: '研发一部',
    },
  },
  'POST /api/logout': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: null,
  },
  'GET /api/userInfo': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: {
      id: 1,
      userCode: 'liwenhai',
      userName: '李文海',
      mobile: '18617320550',
      email: '<EMAIL>',
      createTime: '2024-6-20 12:12:12',
      updateTime: '2024-6-21 13:13:13',
      organizationId: '03-01',
      organizationName: '研发一部',
    },
  },
  // 按照需求文档最新的接口报文结构编写的选单mock数据,选单管理页面获取表格数据
  'POST /api/sysMenuData': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      // {id: null,name: '歡迎',label: 'publish',icon: 'publish',level: null, parentId: null, url: '/welcome',resourceAuth: null,method: 'get', child: [], },
      // {id: null,name: '參數管理',label: 'param',icon: 'param',level: null,parentId: null,url: '/param/demo',resourceAuth: null,method: 'get',
      //   child: [
      //     {id: null,name: 'demo頁面',label: 'demo',icon: null,level: null,parentId: null,url: '/param/demo',resourceAuth: null, method: 'get',},
      //     {id: null,name: '接口配置参数应用服务',label: 'interfaceConfig',url: '/param/interfaceConfig',},
      //     {id: null,name: '接口映射参数应用服务',label: 'interfaceMapping',url: '/param/interfaceMapping',},
      //     { id: null,name: '双发白名单服务',label: 'doubleWhite', url: '/param/doubleWhite', },
      //     { id: null, name: '错误代码映射参数', label: 'errorCodeMap',  url: '/param/errorCodeMap', },
      //     { id: null, name: '转换配置查找', label: 'convertConfig',  url: '/param/convertConfig', },
      //     { id: null, name: '查找配置参数', label: 'searchConfig',  url: '/param/searchConfig', },
      //   ],
      // },
      // { id: null,  name: '系統管理', label: 'system',  url: '/system/role',
      //   child: [
      //     { id: null, name: '角色管理', label: 'role',  url: '/system/role', },
      //     { id: null,  name: '选单管理',  label: 'menu', url: '/system/menu', },
      //   ],
      // },
      { menuId: 1, parentMenuId: 0, menuName: '系統管理', menuType: '0', menuRouteUrl: '/system/role',  iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'system' },
      { menuId: 1001, parentMenuId: 1, menuName: '角色管理', menuType: '0', menuRouteUrl: '/system/role', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'system:role' },
      { menuId: 1002, parentMenuId: 1, menuName: '菜單管理', menuType: '0', menuRouteUrl: '/system/menu', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'system:menu' },
   
      { menuId: 2, parentMenuId: 0, menuName: '參數管理', menuType: '0', menuRouteUrl: '/param/interfaceConfig', iconId: 'TeamOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'param' },
      { menuId: 2001, parentMenuId: 2, menuName: '接口配置参数应用服务', menuType: '0', menuRouteUrl: '/param/interfaceConfig', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'param:interfaceConfig' },
      { menuId: 2002, parentMenuId: 2, menuName: '接口映射参数应用服务', menuType: '0',menuRouteUrl: '/param/interfaceMapping', iconId: 'SettingOutlined', orderSeq: '3', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'param:interfaceMapping' },
      { menuId: 2003, parentMenuId: 2, menuName: '双发白名单服务', menuType: '0', menuRouteUrl: '/param/doubleWhite',iconId: 'SettingOutlined', orderSeq: '4', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'param:doubleWhite' },
      { menuId: 2004, parentMenuId: 2, menuName: '错误代码映射参数', menuType: '0',menuRouteUrl: '/param/errorCodeMap', iconId: 'SettingOutlined', orderSeq: '5', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'param:errorCodeMap' },
      { menuId: 2005, parentMenuId: 2, menuName: '转换配置查找', menuType: '0',menuRouteUrl: '/param/convertConfig', iconId: 'SettingOutlined', orderSeq: '6', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'param:convertConfig' },
      { menuId: 2006, parentMenuId: 2, menuName: '查找配置参数', menuType: '0', menuRouteUrl: '/param/searchConfig',iconId: 'SettingOutlined', orderSeq: '7', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'param:searchConfig' },
      { menuId: 2007, parentMenuId: 2, menuName: 'api密钥映射应用服务', menuType: '0', menuRouteUrl: '/param/apiKeyMapping',iconId: 'SettingOutlined', orderSeq: '7', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'param:apiKeyMapping' },
      { menuId: 2008, parentMenuId: 2, menuName: 'api密钥配置应用服务', menuType: '0', menuRouteUrl: '/param/apiKeyConfig',iconId: 'SettingOutlined', orderSeq: '7', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'param:apiKeyConfig' },
      ],
  },
  'GET /api/routes': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        path: '/login',
        component: '@/pages/login',
      },
      {
        path: '/',
        component: '@/layouts/BasicLayout',
      },
      {
        path: '/welcome',
        name: '歡迎',
        component: '@/pages/welcome',
      },
      {
        path: '/param/demo',
        name: 'demo',
        component: '@/pages/param/demo',
      },
      {
        path: '/param/interfaceConfig',
        name: '接口配置参数应用服务',
        component: '@/pages/param/interfaceConfig',
      },
      {
        path: '/param/interfaceMapping',
        name: '接口映射参数应用服务',
        component: '@/pages/param/interfaceMapping',
      },
      {
        path: '/param/doubleWhite',
        name: '双发白名单服务',
        component: '@/pages/param/doubleWhite',
      },
      {
        path: '/param/errorCodeMap',
        name: '错误代码映射参数',
        component: '@/pages/param/errorCodeMap',
      },
      {
        path: '/param/convertConfig',
        name: '转换配置查找',
        component: '@/pages/param/convertConfig',
      },
      {
        path: '/param/searchConfig',
        name: '查找配置参数',
        component: '@/pages/param/searchConfig',
      },
      {
        path: '/system/role',
        name: '用戶管理',
        component: '@/pages/system/role',
      },
      {
        path: '/system/menu',
        name: '選單管理',
        component: '@/pages/system/menu',
      },
      {
        path: '/other/home',
        name: '儀表盤',
        component: '@/pages/system/menu',
      },
      { path: '/*', component: '@/pages/404' },
    ],
  },
};
