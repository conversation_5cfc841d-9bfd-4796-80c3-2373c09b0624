const users = [
  { id: 0, name: '<PERSON><PERSON>', nickName: 'U', gender: 'MA<PERSON>' },
  { id: 1, name: '<PERSON>', nickName: 'B', gender: 'FEMALE' },
];

export default {
  'GET /api/v1/queryUserList': (req: any, res: any) => {
    res.json({
      success: true,
      data: { list: users },
      errorCode: 0,
    });
  },
  'PUT /api/v1/user/': (req: any, res: any) => {
    res.json({
      success: true,
      errorCode: 0,
    });
  },
  'POST /api/v1/userInfo/': (req: any, res: any) => {
    res.json({
      success: true,
      data: users[0],
      errorCode: 0,
    });
  },
  'POST /api/nq/param/pageQuery': {
    header: {
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        id: 1,
        incId: 303,
        sysInnerRespCode: '11',
        rejRsnCodePri: '2',
        visaRespCode: '33',
        updateUser: ' ',
        mcRespCode: '22',
        updateTime: '20241211143707',
        crcdOrgNo: '101',
        version: 1,
        ecsRespCode: '44',
        createTime: '20241208212106',
        rejRsnCodeDesc: '这是一段描述111222',
        paramSts: '1',
        mcsRespCode: '33',
        rejRsnCode: '0000',
        createUser: ' ',
      },
      {
        id: 12,
        incId: 297,
        sysInnerRespCode: '11',
        rejRsnCodePri: '1111',
        visaRespCode: '11',
        updateUser: ' ',
        mcRespCode: '11',
        updateTime: '20241024150034',
        crcdOrgNo: '013',
        version: 0,
        ecsRespCode: '11',
        createTime: '20241024150034',
        rejRsnCodeDesc: '未知交易渠道',
        paramSts: '1',
        mcsRespCode: '11',
        rejRsnCode: 'B2019124',
        createUser: ' ',
      },
      {
        id: 123,
        incId: 298,
        sysInnerRespCode: '11',
        rejRsnCodePri: '1111',
        visaRespCode: '11',
        updateUser: ' ',
        mcRespCode: '11',
        updateTime: '20241024150034',
        crcdOrgNo: '013',
        version: 0,
        ecsRespCode: '11',
        createTime: '20241024150034',
        rejRsnCodeDesc: '交易检查属性代码参数为空',
        paramSts: '1',
        mcsRespCode: '11',
        rejRsnCode: 'B2019125',
        createUser: ' ',
      },
      {
        id: 1234,
        incId: 299,
        sysInnerRespCode: '11',
        rejRsnCodePri: '1111',
        visaRespCode: '11',
        updateUser: ' ',
        mcRespCode: '11',
        updateTime: '20241024150034',
        crcdOrgNo: '013',
        version: 0,
        ecsRespCode: '11',
        createTime: '20241024150034',
        rejRsnCodeDesc: '系统异常',
        paramSts: '1',
        mcsRespCode: '11',
        rejRsnCode: 'X2019999',
        createUser: ' ',
      },
      {
        id: 12314,
        incId: 298,
        sysInnerRespCode: '11',
        rejRsnCodePri: '1111',
        visaRespCode: '11',
        updateUser: ' ',
        mcRespCode: '11',
        updateTime: '20241024150034',
        crcdOrgNo: '013',
        version: 0,
        ecsRespCode: '11',
        createTime: '20241024150034',
        rejRsnCodeDesc: '交易检查属性代码参数为空',
        paramSts: '1',
        mcsRespCode: '11',
        rejRsnCode: 'B2019125',
        createUser: ' ',
      },
      {
        id: 12342,
        incId: 298,
        sysInnerRespCode: '11',
        rejRsnCodePri: '1111',
        visaRespCode: '11',
        updateUser: ' ',
        mcRespCode: '11',
        updateTime: '20241024150034',
        crcdOrgNo: '013',
        version: 0,
        ecsRespCode: '11',
        createTime: '20241024150034',
        rejRsnCodeDesc: '交易检查属性代码参数为空',
        paramSts: '1',
        mcsRespCode: '11',
        rejRsnCode: 'B2019125',
        createUser: ' ',
      },
      {
        id: 12343,
        incId: 298,
        sysInnerRespCode: '11',
        rejRsnCodePri: '1111',
        visaRespCode: '11',
        updateUser: ' ',
        mcRespCode: '11',
        updateTime: '20241024150034',
        crcdOrgNo: '013',
        version: 0,
        ecsRespCode: '11',
        createTime: '20241024150034',
        rejRsnCodeDesc: '交易检查属性代码参数为空',
        paramSts: '1',
        mcsRespCode: '11',
        rejRsnCode: 'B2019125',
        createUser: ' ',
      },
      {
        id: 12234,
        incId: 298,
        sysInnerRespCode: '11',
        rejRsnCodePri: '1111',
        visaRespCode: '11',
        updateUser: ' ',
        mcRespCode: '11',
        updateTime: '20241024150034',
        crcdOrgNo: '013',
        version: 0,
        ecsRespCode: '11',
        createTime: '20241024150034',
        rejRsnCodeDesc: '交易检查属性代码参数为空',
        paramSts: '1',
        mcsRespCode: '11',
        rejRsnCode: 'B2019125',
        createUser: ' ',
      },
      {
        id: 11234,
        incId: 298,
        sysInnerRespCode: '11',
        rejRsnCodePri: '1111',
        visaRespCode: '11',
        updateUser: ' ',
        mcRespCode: '11',
        updateTime: '20241024150034',
        crcdOrgNo: '013',
        version: 0,
        ecsRespCode: '11',
        createTime: '20241024150034',
        rejRsnCodeDesc: '交易检查属性代码参数为空',
        paramSts: '1',
        mcsRespCode: '11',
        rejRsnCode: 'B2019125',
        createUser: ' ',
      },
      {
        id: 122342,
        incId: 298,
        sysInnerRespCode: '11',
        rejRsnCodePri: '1111',
        visaRespCode: '11',
        updateUser: ' ',
        mcRespCode: '11',
        updateTime: '20241024150034',
        crcdOrgNo: '013',
        version: 0,
        ecsRespCode: '11',
        createTime: '20241024150034',
        rejRsnCodeDesc: '交易检查属性代码参数为空',
        paramSts: '1',
        mcsRespCode: '11',
        rejRsnCode: 'B2019125',
        createUser: ' ',
      },
    ],
  },
};
