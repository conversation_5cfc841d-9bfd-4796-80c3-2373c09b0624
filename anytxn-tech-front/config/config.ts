import { defineConfig } from '@umijs/max';
import routes from './routes';

export default defineConfig({
  title: false,
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  // layout: {
  //   // 指定自定义布局组件
  //   layout: '@/layouts/BasicLayout', // 替换为你的布局文件路径
  // },
  routes,
  plugins: [],
  // https://umijs.org/zh-CN/plugins/plugin-locale
  locale: {
    default: 'en-US',
    antd: true,
    baseNavigator: true,
    baseSeparator: '-',
    useLocalStorage: true,
  },
  mock: false, 
  // proxy: {
  //   '/api': {
  //     // 网关测试环境
  //     // target: 'http://10.0.30.2:31125',
  //     // 网关开发环境
  //     // target: 'http://10.0.30.8:30125',
  //     // 容器云开发环境
  //     // target: 'http://10.0.30.2:30814',
  //     target: 'http://10.0.30.2:30814',
  //     // 容器云测试环境
  //     // target: 'http://192.168.187.161:8080',
  //     changeOrigin: true,
  //     pathRewrite: { '^/api': '' },
  //     timeout: 30000, // 增加超时时间到30秒
  //   },
  // },
  npmClient: 'npm',
});
