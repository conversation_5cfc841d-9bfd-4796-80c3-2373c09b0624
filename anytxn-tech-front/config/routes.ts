export default [
  // {
  //   path: '/login',
  //   component: '@/pages/login',
  // },
  {
    path: '/',
    component: '@/layouts/BasicLayout',
    routes: [
      { path: '/', redirect: '/param/interfaceConfig' },
      {
        path: '/welcome',
        name: '歡迎',
        component: '@/pages/welcome',
      },
      {
        path: '/param',
        name: '參數管理',
        routes: [
          {
            path: '/param/demo',
            name: 'demo',
            component: '@/pages/param/demo',
          },
          {
            path: '/param/interfaceConfig',
            name: '接口配置参数应用服务',
            component: '@/pages/param/interfaceConfig',
          },
          {
            path: '/param/interfaceMapping',
            name: '接口映射参数应用服务',
            component: '@/pages/param/interfaceMapping',
          },
          {
            path: '/param/doubleWhite',
            name: '双发白名单服务',
            component: '@/pages/param/doubleWhite',
          },
          {
            path: '/param/errorCodeMap',
            name: '错误代码映射参数',
            component: '@/pages/param/errorCodeMap',
          },
          {
            path: '/param/convertConfig',
            name: '转换配置查找',
            component: '@/pages/param/convertConfig',
          },
          {
            path: '/param/searchConfig',
            name: '查找配置参数',
            component: '@/pages/param/searchConfig',
          },
          {
            path: '/param/apiKeyMapping',
            name: 'api密钥映射应用服务',
            component: '@/pages/param/apiKeyMapping',
          },
          {
            path: '/param/apiKeyConfig',
            name: 'api密钥配置应用服务',
            component: '@/pages/param/apiKeyConfig',
          },
          {
            path: '/param/cardIssuingNetManage',
            name: '網絡管理',
            component: '@/pages/param/communication/cardIssuingNetManage',
          },
          {
            path: '/param/chanelStatus',
            name: '渠道签到状态查询（交易网关）',
            component: '@/pages/param/communication/chanelStatus',
          },
          {
            path: '/param/sfmWebSite',
            name: 'SFM签入/簽出',
            component: '@/pages/param/communication/sfmWebSite',
          },
          {
            path: '/param/channelComStsQuery',
            name: '渠道签到状态查询（出口网关）',
            component: '@/pages/param/communication/channelComStsQuery',
          },
        ],
      },
      {
        path: '/system',
        name: '系統管理',
        routes: [
          {
            path: '/system/role',
            name: '用戶管理',
            component: '@/pages/system/role',
          },
          {
            path: '/system/menu',
            name: '選單管理',
            component: '@/pages/system/menu',
          },
        ],
      },
      {
        path: '/other',
        name: '其它',
        routes: [
          {
            path: '/other/home',
            name: '儀表盤',
            component: '@/pages/system/menu',
          },
        ],
      },
    ],
  },
  { path: '/*', component: '@/pages/404' },
];